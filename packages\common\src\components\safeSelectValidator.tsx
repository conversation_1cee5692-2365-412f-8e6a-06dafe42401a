import { MenuItem, Select } from "@mui/material";
import React, { FC, ReactNode } from "react";

// Define a type that mimics the SelectValidatorProps
interface SelectValidatorProps {
  validators?: string[];
  errorMessages?: string[];
  value: any;
  onChange: (event: any) => void;
  children?: ReactNode;
  [key: string]: any;
}

// Create a component that mimics the SelectValidator
const SelectValidator: FC<SelectValidatorProps> = (props) => {
  const { validators, errorMessages, ...otherProps } = props;
  return <Select {...otherProps}>{props.children}</Select>;
};

/**
 * Props for the SafeSelectValidator component
 */
interface SafeSelectValidatorProps extends SelectValidatorProps {
  children?: ReactNode;
}

/**
 * A safer wrapper around SelectValidator that ensures it always has children
 * to prevent common errors with the SelectValidator component
 */
const SafeSelectValidator: FC<SafeSelectValidatorProps> = (props) => {
  const { children, ...otherProps } = props;

  // Check if children exist and are not empty
  const hasValidChildren = React.Children.count(children) > 0;

  return (
    <SelectValidator {...otherProps}>
      {hasValidChildren ? children : <MenuItem value="">No options available</MenuItem>}
    </SelectValidator>
  );
};

export default SafeSelectValidator;
