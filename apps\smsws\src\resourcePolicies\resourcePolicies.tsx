import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  <PERSON>u<PERSON>tem,
  <PERSON><PERSON>ield,
  Typography,
} from "@mui/material";
import { MaterialTable } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscResourcePoliciesService } from "@pnmui/common/services/smsc/resourcePoliciesService";
import React, { ChangeEvent, useEffect, useState } from "react";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { toast } from "react-toastify";
import { tap } from "rxjs/operators";
import { ResourcePolicy } from "../types";

interface TableData {
  data: ResourcePolicy[];
  page: number;
  totalCount: number;
}

function SmscResourcePolicies() {
  const [originalPolicies, setOriginalPolicies] = useState<ResourcePolicy[]>(
    []
  );
  const [policies, setPolicies] = useState<ResourcePolicy[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const [hostName, setHostName] = useState<string>("");
  const tableRefPolicies = React.createRef<any>();

  useEffect(() => {
    const subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });
    populatePolicies();
    return () => subscription.unsubscribe();
  }, []);

  async function populatePolicies(): Promise<void> {
    try {
      const policiesResponse =
        await SmscResourcePoliciesService.getResourcePolicies();
      const policiesList = Array.isArray(policiesResponse)
        ? policiesResponse
        : [];
      setPolicies(policiesList);
      setOriginalPolicies(policiesList);
    } catch (error: any) {
      console.error("Error fetching resource policies:", error);
      toast.error(
        i18n["smsc.retryProfileForm.SaveFailure"] ||
          "Failed to fetch resource policies"
      );
    }
  }

  async function handleReload(event: React.FormEvent): Promise<void> {
    event.preventDefault();
    try {
      await SmscResourcePoliciesService.reloadResourcePolicies(hostName);
      toast.success(
        i18n["smsc.retryProfileForm.SaveSuccess"] ||
          "Resource policies reloaded successfully"
      );
      await populatePolicies();
    } catch (error: any) {
      console.error("Error reloading resource policies:", error);
      toast.error(
        i18n["smsc.retryProfileForm.SaveFailure"] ||
          "Failed to reload resource policies"
      );
    }
  }

  const handleRowSelection = (rows) => {
    setSelectedRows(rows.map((row) => row.id));
  };

  // Removed the handleDelete function and Delete button as per requirements

  useEffect(() => {
    if (tableRefPolicies.current) {
      tableRefPolicies.current.onQueryChange();
    }
  }, [policies]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search() {
    const filteredPolicies = originalPolicies.filter((policy) =>
      policy.name.toLowerCase().includes(selectedName.toLowerCase())
    );
    setPolicies(filteredPolicies);
  }

  return (
    <div className="wrapper">
      <Typography
        variant="body1"
        style={{
          padding: "1em 1.5em",
          color: "#666",
          lineHeight: "1.5",
        }}
      >
        {i18n["smsc.resourcepolicy.description"] ||
          " The Resource Manager is used to find a host that provides a resource of M2PC_oam. Resource Policies are loaded from that file system into the database on startup. If resource policies are added to the file (tango/config/ISMSC_resourcePolicy.cfg) a Reload of Resource Policies from the file should be performed. Use the button below to reload the Resource Policies. If you require them to be loaded from a specific host please provide the host name.\n"}
      </Typography>

      <Card className="content-card">
        <CardContent>
          <ValidatorForm onSubmit={handleReload} className="form-row">
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                flexDirection: "column",
                alignItems: "center",
                marginBottom: "1rem",
              }}
            >
              <TextField
                label={i18n["smsc.resourcePolicies.hostName"] || "Host name"}
                variant="outlined"
                value={hostName}
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setHostName(e.target.value)
                }
                style={{ marginRight: "1rem", minWidth: "15em" }}
              />

              <Button
                variant="contained"
                color="primary"
                type="submit"
                className="request-handler-reload-button"
                aria-label="Load from File"
                disabled={
                  !SecurityService.checkPermission(
                    "SMSC_RESOURCE_POLICIES_READ_PERMISSION"
                  ) || policies.length > 0
                }
                style={{ display: policies.length > 0 ? 'none' : 'inline-flex' }}
              >
                {i18n["button.reload"] || "Load from File"}
              </Button>
              <SelectValidator
                label={i18n["smsc.resourcePolicies.list.name"] || "Name"}
                margin="normal"
                variant="outlined"
                style={{ minWidth: "15em", marginRight: "1rem" }}
                children={["Any", ...originalPolicies.map((p) => p.name)].map(
                  (name) => (
                    <MenuItem key={name} value={name === "Any" ? "" : name}>
                      {name}
                    </MenuItem>
                  )
                )}
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setSelectedName(e.target.value)
                }
                value={selectedName}
              />
            </div>
            <br />

          </ValidatorForm>
        </CardContent>

        <CardContent>
          <MaterialTable
            tableRef={tableRefPolicies}
            data={() => ({
              data: policies,
              totalCount: policies.length,
            })}
            columns={[{ field: "name", title: "Name" }]}
            options={{
              selection: true,
              pagination: true,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              actionsColumnIndex: -1,
              toolbar: false,
            }}
            onSelectionChange={(rows) => handleRowSelection(rows)}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default SmscResourcePolicies;
