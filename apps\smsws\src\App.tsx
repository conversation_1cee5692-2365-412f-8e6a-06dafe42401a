import { CircularProgress, createTheme, ThemeProvider } from "@mui/material";
import { useEffect, useState } from "react";
import {
  Route,
  BrowserRouter as Router,
  Routes,
} from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./App.css";
import "./common.css";

// Import layout components
import Layout from "./components/layout/Layout";

// Import page components
import SmscApplicationForm from "./applications/applicationForm";
import SmscApplications from "./applications/applications";
// Import all page components
import SmscConnectionForm from "./connections/connectionForm";
import SmscConnections from "./connections/connections";
import SmscDeliveryAndRetryProfileForm from "./deliveryAndRetryProfiles/deliveryAndRetryProfileForm";
import SmscDeliveryAndRetryProfiles from "./deliveryAndRetryProfiles/deliveryAndRetryProfiles";
import SmscDeliveryAndRetryScheduleForm from "./deliveryAndRetrySchedules/deliveryAndRetryScheduleForm";
import SmscDeliveryAndRetrySchedules from "./deliveryAndRetrySchedules/deliveryAndRetrySchedules";
import SmscIpLists from "./ipLists/ipLists";
import SmscIpListForm from "./ipLists/ipListsForm";
import SmscNumberListForm from "./numberLists/numberListForm";
import SmscNumberLists from "./numberLists/numberLists";
import SmscResourcePolicies from "./resourcePolicies/resourcePolicies";
import SmscResourcePolicyForm from "./resourcePolicies/resourcePolicyForm";
import SmscRoutingClasses from "./routingClasses/routingClasses";
import SmscRoutingClassForm from "./routingClasses/routingClassForm";
import SmscSmppApplicationProfileForm from "./smppApplicationProfiles/smppApplicationProfileForm";
import SmscSmppApplicationProfiles from "./smppApplicationProfiles/smppApplicationProfiles";
import SmscSmsRoutingTableForm from "./smsRoutingTables/smsRoutingTableForm";
import SmscSmsRoutingTables from "./smsRoutingTables/smsRoutingTables";
import SmscSpamWordForm from "./spamWords/spamWordForm";
import SmscSpamWords from "./spamWords/spamWords";
import SmscStripAndReplace from "./stripAndReplace/stripAndReplace";
import SmscNumberAnalysisForm from "./numberAnalysis/numberAnalysisForm";

// Import services
import { PropertiesService } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";

export default function App() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log("App: Component mounted");

    // Initialize services
    Promise.all([PropertiesService.init(), SecurityService.init()])
      .then(() => {
        console.log("App: Services initialized successfully");
        setLoading(false);
      })
      .catch((error) => {
        console.error("App: Error initializing services:", error);
        setLoading(false);
      });
  }, []);

  const defaultMaterialTheme = createTheme({
    palette: {
      primary: {
        main: "#1976d2",
      },
      secondary: {
        main: "#f50057",
      },
    },
  });

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </div>
    );
  }

  return (
    <ThemeProvider theme={defaultMaterialTheme}>
      <Router>
        <Routes>
          <Route path="/" element={<Layout />}>
            {/* Applications */}
            <Route index element={<SmscApplications />} />
            <Route path="/smscApplications" element={<SmscApplications />} />
            <Route path="/smscApplicationForm/:id" element={<SmscApplicationForm />} />

            {/* Connections */}
            <Route path="/smscConnections" element={<SmscConnections />} />
            <Route path="/smscConnectionForm/:id" element={<SmscConnectionForm />} />

            {/* Delivery and Retry Profiles */}
            <Route path="/smscDeliveryAndRetryProfiles" element={<SmscDeliveryAndRetryProfiles />} />
            <Route path="/smscDeliveryAndRetryProfileForm/:id" element={<SmscDeliveryAndRetryProfileForm />} />

            {/* Delivery and Retry Schedules */}
            <Route path="/smscDeliveryAndRetrySchedules" element={<SmscDeliveryAndRetrySchedules />} />
            <Route path="/smscDeliveryAndRetryScheduleForm/:id" element={<SmscDeliveryAndRetryScheduleForm />} />

            {/* IP Lists */}
            <Route path="/smscIpLists" element={<SmscIpLists />} />
            <Route path="/smscIpListsForm/:id" element={<SmscIpListForm />} />

            {/* Number Lists */}
            <Route path="/smscNumberLists" element={<SmscNumberLists />} />
            <Route path="/smscNumberListForm/:id" element={<SmscNumberListForm />} />
            {/* Add compatibility route for old URL pattern */}
            <Route path="/smscNumberListsForm/:id" element={<SmscNumberListForm />} />

            {/* Resource Policies */}
            <Route path="/smscResourcePolicies" element={<SmscResourcePolicies />} />
            <Route path="/smscResourcePolicyForm/:id" element={<SmscResourcePolicyForm />} />

            {/* Routing Classes */}
            <Route path="/smscRoutingClasses" element={<SmscRoutingClasses />} />
            <Route path="/smscRoutingClassForm/:id" element={<SmscRoutingClassForm />} />

            {/* SMPP Application Profiles */}
            <Route path="/smscSmppApplicationProfiles" element={<SmscSmppApplicationProfiles />} />
            <Route path="/smscSmppApplicationProfileForm/:id" element={<SmscSmppApplicationProfileForm />} />

            {/* SMS Routing Tables */}
            <Route path="/smscSmsRoutingTables" element={<SmscSmsRoutingTables />} />
            <Route path="/smscSmsRoutingTableForm/:id" element={<SmscSmsRoutingTableForm />} />
            {/* Add compatibility route for old URL pattern */}
            <Route path="/routing/table/form/:id" element={<SmscSmsRoutingTableForm />} />

            {/* Spam Words */}
            <Route path="/smscSpamWords" element={<SmscSpamWords />} />
            <Route path="/smscSpamWordForm/:id" element={<SmscSpamWordForm />} />
            <Route path="/smscSpamWordRegistriesForm/:id" element={<SmscSpamWordForm />} />

            {/* Strip and Replace */}
            <Route path="/smscStripAndReplace/:type" element={<SmscStripAndReplace />} />

            {/* Number Analysis */}
            <Route path="/smscNumberAnalysis" element={<SmscNumberAnalysisForm />} />
          </Route>
        </Routes>
        <ToastContainer position="top-right" autoClose={3000} />
      </Router>
    </ThemeProvider>
  );
}
