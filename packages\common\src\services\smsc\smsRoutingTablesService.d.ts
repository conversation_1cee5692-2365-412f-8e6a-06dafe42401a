import { RoutingClass, SMSRoutingTable } from "@pnmui/common/types/smscTypes";
export declare const SmscSmsRoutingTablesService: {
    getSmsRoutingTables(): Promise<SMSRoutingTable[]>;
    getSmsRoutingTableById(id: string | number): Promise<SMSRoutingTable | null>;
    saveSmsRoutingTable(smsRoutingTable: SMSRoutingTable): Promise<SMSRoutingTable>;
    deleteSmsRoutingTableById(id: string | number): Promise<boolean>;
    saveRoutingClassWithName(routingClassName: string): Promise<RoutingClass>;
};
