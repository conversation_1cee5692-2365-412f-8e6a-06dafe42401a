declare module 'smsws/ApplicationsPage' {
  const ApplicationsPage: React.ComponentType;
  export default ApplicationsPage;
}

declare module 'smsws/ApplicationForm' {
  const ApplicationForm: React.ComponentType;
  export default ApplicationForm;
}

declare module 'smsws/ConnectionsPage' {
  const ConnectionsPage: React.ComponentType;
  export default ConnectionsPage;
}

declare module 'smsws/ConnectionForm' {
  const ConnectionForm: React.ComponentType;
  export default ConnectionForm;
}

declare module 'smsws/NumberAnalysisForm' {
  const NumberAnalysisForm: React.ComponentType;
  export default NumberAnalysisForm;
}

declare module 'smsws/MicroFrontend' {
  export const MicroFrontendWrapper: React.ComponentType<{ children: React.ReactNode }>;
  export const ApplicationsPage: React.ComponentType;
  export const ApplicationForm: React.ComponentType;
  export const ConnectionsPage: React.ComponentType;
  export const ConnectionForm: React.ComponentType;
  export const NumberAnalysisForm: React.ComponentType;
}
