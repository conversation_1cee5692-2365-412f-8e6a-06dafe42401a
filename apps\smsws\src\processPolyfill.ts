// This file provides a polyfill for the Node.js process object in the browser
// It should be imported before any other files that might use process.env

console.log('processPolyfill: Initializing process polyfill');

// Create a minimal process object with an env object containing our environment variables
if (typeof window !== 'undefined') {
  try {
    if (typeof (window as any).process === 'undefined') {
      console.log('processPolyfill: Creating new process object');
      (window as any).process = {
        env: {
          // Explicitly set environment variables
          NODE_ENV: import.meta.env.NODE_ENV || 'development',
          REACT_APP_PROXY_URL: import.meta.env.REACT_APP_PROXY_URL || 'http://localhost:3500',
          // Never use mock data
          REACT_APP_MOCK: 'false'
        },
        // Add other process properties as needed
        browser: true,
        version: '',
        platform: 'browser'
      };
    } else {
      console.log('processPolyfill: Process object already exists, ensuring env variables');
      // Ensure environment variables are set if process already exists
      const processEnv = (window as any).process.env;
      if (!processEnv.NODE_ENV) processEnv.NODE_ENV = import.meta.env.NODE_ENV || 'development';
      if (!processEnv.REACT_APP_PROXY_URL) processEnv.REACT_APP_PROXY_URL = import.meta.env.REACT_APP_PROXY_URL || 'http://localhost:3500';
      // Never use mock data
      processEnv.REACT_APP_MOCK = 'false';
    }

    console.log('processPolyfill: Environment variables set:', (window as any).process.env);
  } catch (error) {
    console.error('processPolyfill: Error setting up process polyfill:', error);
    // Create a minimal fallback process object
    (window as any).process = {
      env: {
        NODE_ENV: 'development',
        REACT_APP_PROXY_URL: 'http://localhost:3500',
        REACT_APP_MOCK: 'false'
      },
      browser: true
    };
  }
}

export { };

