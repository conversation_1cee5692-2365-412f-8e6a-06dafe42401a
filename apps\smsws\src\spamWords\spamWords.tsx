import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, MenuItem } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscSpamWordsService } from "@pnmui/common/services/smsc/spamWordsService";
import { paginate } from "@pnmui/common/utils";
import React, { ChangeEvent, useEffect, useState } from "react";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import { SpamWordRegistry } from "../types";

interface TableData {
  data: SpamWordRegistry[];
  page: number;
  totalCount: number;
}

function SmscSpamWords() {
  const [originalProfiles, setOriginalProfiles] = useState<SpamWordRegistry[]>(
    []
  );
  const [profiles, setProfiles] = useState<SpamWordRegistry[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const navigate = useNavigate();
  const tableRefProfiles = React.createRef<any>();

  useEffect(() => {
    const subscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });
    populateProfiles();

    return () => subscription.unsubscribe();
  }, []);

  async function populateProfiles(): Promise<void> {
    try {
      const profilesResponse = await SmscSpamWordsService.getSpamWords();
      console.log("profilesResponse", profilesResponse);
      const profilesList: SpamWordRegistry[] = Array.isArray(profilesResponse)
        ? profilesResponse
        : [];
      setProfiles(profilesList);
      setOriginalProfiles(profilesList);
    } catch (error) {
      console.error("Error fetching profiles:", error);
    }
  }

  async function populateProfilesData(query: {
    page: number;
    pageSize: number;
  }): Promise<TableData> {
    const paginatedList = paginate(profiles, query.page, query.pageSize);
    return {
      data: paginatedList,
      page: query.page,
      totalCount: profiles.length,
    };
  }

  const handleRowSelection = (rows: SpamWordRegistry[]): void => {
    setSelectedRows(rows.map((row) => row.id || ""));
  };

  const handleDelete = async (): Promise<void> => {
    if (selectedRows.length === 0) {
      console.error("No rows selected for deletion.");
      return;
    }
    if (window.confirm("Are you sure you want to delete?")) {
      try {
        await Promise.all(
          selectedRows.map(async (id) => {
            return await SmscSpamWordsService.deleteSpamWordRegistry(id);
          })
        );
        const updatedProfiles = profiles.filter(
          (profile) =>
            profile.id !== undefined && selectedRows.indexOf(profile.id) === -1
        );
        setProfiles([...updatedProfiles]);
        setSelectedRows([]);
        toast.success("Rows deleted successfully!");
      } catch (error: any) {
        console.error(error);
        toast.error(error.message);
      }
    }
  };

  useEffect(() => {
    tableRefProfiles.current && tableRefProfiles.current.onQueryChange();
  }, [profiles]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search(): void {
    const filteredProfiles = originalProfiles.filter((profile) =>
      profile.name?.toLowerCase().includes(selectedName.toLowerCase())
    );
    setProfiles(filteredProfiles);
  }

  const columns = [
    {
      field: "name",
      title: "Name",
    },
    {
      field: "caseSensitive",
      title: "Case Sensitive",
      render: (rowData) => (
        <div>
          {rowData.caseSensitive ? (
            <CheckIcon style={{ color: "green" }} />
          ) : (
            <CloseIcon style={{ color: "#9e9e9e" }} />
          )}
        </div>
      ),
    },
    {
      field: "countMultipleOccurences",
      title: "Count Multiples",
      render: (rowData) => (
        <div>
          {rowData.countMultipleOccurences ? (
            <CheckIcon style={{ color: "green" }} />
          ) : (
            <CloseIcon style={{ color: "#9e9e9e" }} />
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="wrapper">
      <Card className="content-card">
        <div className="form-row">
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>
              <ValidatorForm onSubmit={() => {}}>
                <SelectValidator
                  label={i18n["smsc.spamWords.spamWordList.name"] || "Name"}
                  margin="normal"
                  variant="outlined"
                  style={{ minWidth: "15em", marginRight: "1rem" }}
                  children={["Any", ...profiles.map((c) => c.name)].map(
                    (name) => (
                      <MenuItem key={name} value={name === "Any" ? "" : name}>
                        {name}
                      </MenuItem>
                    )
                  )}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setSelectedName(e.target.value)
                  }
                  value={selectedName}
                />
              </ValidatorForm>
            </div>
          </div>
        </div>

        <CardContent>
          <MaterialTable
            tableRef={tableRefProfiles}
            icons={tableIcons}
            data={populateProfilesData}
            columns={columns} // Use the updated columns
            options={{
              selection: true,
              actionsColumnIndex: -1,
              toolbar: false,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              emptyRowsWhenPaging: false,
              headerStyle: {
                fontWeight: "bold",
                textAlign: "center",
              },
              cellStyle: {
                textAlign: "center",
              },
            }}
            onRowClick={(event, rowData) => {
              navigate(`/smscSpamWordForm/${rowData.id}`);
            }}
            onSelectionChange={(rows) => handleRowSelection(rows)}
          />
        </CardContent>

        <Button
          variant="contained"
          color="primary"
          className="request-handler-add-button"
          aria-label="Add"
          onClick={() => navigate("/smscSpamWordForm/new")}
          style={{
            marginLeft: "15px",
            marginTop: "2rem",
            marginBottom: "1rem",
          }}
          disabled={
            !SecurityService.checkPermission(
              "SMSC_SPAM_REGISTRIES_CREATE_PERMISSION"
            )
          }
        >
          {i18n["button.add"] || "Add"}
        </Button>

        <span style={{ marginLeft: "10px" }}>
          <Button
            variant="contained"
            color="secondary"
            type="button"
            onClick={handleDelete}
            style={{ marginTop: "2rem", marginBottom: "1rem" }}
            disabled={
              !SecurityService.checkPermission(
                "SMSC_SPAM_REGISTRIES_DELETE_PERMISSION"
              )
            }
          >
            {i18n["button.delete"] || "Delete"}
          </Button>
        </span>
      </Card>
    </div>
  );
}

export default SmscSpamWords;
