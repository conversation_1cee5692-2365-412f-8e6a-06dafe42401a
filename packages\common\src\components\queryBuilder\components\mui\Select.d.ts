import { ValueSelectorProps, FieldSelectorProps, OperatorSelectorProps } from "../../types";
interface SelectOption {
    value: string;
    label: string;
}
interface SelectProps extends ValueSelectorProps, FieldSelectorProps, OperatorSelectorProps {
    selectedValue?: string;
    values?: SelectOption[];
}
export declare function Select({ handleOnChange: onChange, value: selectedValue, options: values }: SelectProps): import("react/jsx-runtime").JSX.Element;
export {};
