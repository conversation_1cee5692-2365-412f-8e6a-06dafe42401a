import React from "react";
interface Weekday {
    value?: number;
}
interface DataItem {
    inEdit?: boolean;
    start?: string;
    end?: string;
    startDate?: string;
    endDate?: string;
    weekdays?: Array<Weekday | number>;
    [key: string]: any;
}
interface DurationCellProps {
    dataItem: DataItem;
    dataIndex?: number;
    onUpdate: (dataItem: DataItem, dataIndex?: number) => void;
}
declare const DurationCell: React.FC<DurationCellProps>;
export default DurationCell;
