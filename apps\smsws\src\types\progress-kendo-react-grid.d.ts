declare module '@progress/kendo-react-grid' {
  import * as React from 'react';

  export interface GridProps {
    data?: any[];
    style?: React.CSSProperties;
    onItemChange?: (event: GridItemChangeEvent) => void;
    editField?: string;
    children?: React.ReactNode;
    [key: string]: any;
  }

  export interface GridItemChangeEvent {
    dataItem: any;
    field?: string;
    value?: any;
    syntheticEvent?: React.SyntheticEvent;
  }

  export const Grid: React.ComponentType<GridProps>;
  export const GridToolbar: React.ComponentType<any>;
}

declare module '@progress/kendo-react-grid/dist/npm/GridColumn' {
  import * as React from 'react';

  export interface GridColumnProps {
    field?: string;
    title?: string;
    width?: string | number;
    cell?: React.ComponentType<any>;
    children?: React.ReactNode;
    filterable?: boolean;
    [key: string]: any;
  }

  export const GridColumn: React.ComponentType<GridColumnProps>;
}
