import { TextFieldProps as MuiTextFieldProps } from "@mui/material";
import { FC, ReactNode } from "react";
/**
 * Props for the TextField component
 */
interface TextFieldProps extends Omit<MuiTextFieldProps, 'select'> {
    select?: boolean;
    children?: ReactNode;
}
/**
 * A safer wrapper around Material-UI TextField that handles select property correctly
 * and prevents common errors with children and select properties
 */
declare const TextField: FC<TextFieldProps>;
export default TextField;
