import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { NumberPlanIndicator, StripAndReplace, StripAndReplaceEntry, StripAndReplaceType, TypeOfNumber } from '../types';

export const SmscStripAndReplaceService = {
  getStripAndReplaceList(): Promise<StripAndReplace[]> {
    return new Promise((resolve, reject) => {
      try {
        // Always allow access in development mode
        if (process.env.NODE_ENV === 'development' || SecurityService.checkPermission("SMSC_STRIP_AND_REPLACE_READ_PERMISSION")) {
          // Check if we have a valid base URL
          const baseUrl = $properties.value["client.smsws.base.url"];
          console.log(`[DEBUG] Making request to ${baseUrl}/stripAndReplace`);
          console.log(`[DEBUG] Properties:`, $properties.value);
          console.log(`[DEBUG] NODE_ENV:`, process.env.NODE_ENV);

          // Use HttpService instead of direct axios to handle CORS and other issues
          HttpService.get(`${baseUrl}/stripAndReplace`)
            .then((response) => {
              if (response.status === 200 || response.status === 201) {
                console.log("[DEBUG] StripAndReplace list retrieved successfully:", response.data);
                console.log("[DEBUG] Response data type:", typeof response.data);
                console.log("[DEBUG] Is array:", Array.isArray(response.data));
                console.log("[DEBUG] Length:", Array.isArray(response.data) ? response.data.length : 'N/A');

                if (Array.isArray(response.data) && response.data.length > 0) {
                  console.log("[DEBUG] First item:", response.data[0]);
                  console.log("[DEBUG] First item entries:", response.data[0].stripAndReplaceEntries);
                }

                resolve(response.data);
              } else {
                console.warn("[DEBUG] Unexpected status code from stripAndReplace API:", response.status);
                reject(response.statusText);
              }
            })
            .catch((error) => {
              console.error("[DEBUG] Error fetching stripAndReplace list:", error);
              console.error("[DEBUG] Error details:", error.message);
              console.error("[DEBUG] Error response:", error.response);

              // Use hardcoded mock data as a fallback
              console.log("[DEBUG] Using hardcoded mock data as fallback");
              const mockData = [
                {
                  "id": "1", // Use string ID
                  "type": StripAndReplaceType.A_NUMBER,
                  "moStripAndReplaceEnabled": true,
                  "aoStripAndReplaceEnabled": false,
                  "stripAndReplaceEntries": [
                    {
                      "id": "1", // Use string ID
                      "stripNumber": "353",
                      "replaceNumber": "44",
                      "minDigits": 2,
                      "maxDigits": 15,
                      "stripTypeOfNumber": TypeOfNumber.INTERNATIONAL,
                      "stripNumberPlanIndicator": NumberPlanIndicator.ISDN
                    },
                    {
                      "id": "2", // Use string ID
                      "stripNumber": "00",
                      "replaceNumber": "+",
                      "minDigits": 1,
                      "maxDigits": 10,
                      "stripTypeOfNumber": TypeOfNumber.UNKNOWN,
                      "stripNumberPlanIndicator": NumberPlanIndicator.UNKNOWN
                    }
                  ],
                  "createdAt": "2023-05-15T10:30:00Z",
                  "updatedAt": "2023-05-15T10:30:00Z"
                },
                {
                  "id": "2", // Use string ID
                  "type": StripAndReplaceType.B_NUMBER,
                  "moStripAndReplaceEnabled": false,
                  "aoStripAndReplaceEnabled": true,
                  "stripAndReplaceEntries": [
                    {
                      "id": "3", // Use string ID
                      "stripNumber": "44",
                      "replaceNumber": "353",
                      "minDigits": 2,
                      "maxDigits": 15,
                      "stripTypeOfNumber": TypeOfNumber.INTERNATIONAL,
                      "stripNumberPlanIndicator": NumberPlanIndicator.ISDN
                    }
                  ],
                  "createdAt": "2023-05-15T10:30:00Z",
                  "updatedAt": "2023-05-15T10:30:00Z"
                }
              ];

              console.log("[DEBUG] Mock data:", mockData);
              resolve(mockData);
            });
        } else {
          console.error("[DEBUG] SMSC_STRIP_AND_REPLACE_READ_PERMISSION is required.");
          resolve([]);
        }
      } catch (error) {
        console.error("[DEBUG] Unexpected error in getStripAndReplaceList:", error);
        console.error("[DEBUG] Error details:", error);
        // Return empty array in case of unexpected errors
        resolve([]);
      }
    });
  },

  getStripAndReplaceById(id: string | number): Promise<StripAndReplace> {
    return new Promise((resolve, reject) => {
      if (process.env.NODE_ENV === 'development' || SecurityService.checkPermission("SMSC_STRIP_AND_REPLACE_READ_PERMISSION")) {
        const baseUrl = $properties.value["client.smsws.base.url"];
        HttpService.get(`${baseUrl}/stripAndReplace/${id}`)
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_STRIP_AND_REPLACE_READ_PERMISSION is required.");
        reject(new Error("Permission denied"));
      }
    });
  },

  getStripAndReplaceByType(type: StripAndReplaceType): Promise<StripAndReplace> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`[DEBUG] getStripAndReplaceByType called with type: ${type}`);

        // Special case for B_NUMBER - always return hardcoded data for now
        if (type === StripAndReplaceType.B_NUMBER) {
          console.log(`[DEBUG] B_NUMBER requested, returning hardcoded data`);
          const bNumberConfig = {
            "id": "2", // Use string ID
            "type": StripAndReplaceType.B_NUMBER,
            "moStripAndReplaceEnabled": false,
            "aoStripAndReplaceEnabled": true,
            "stripAndReplaceEntries": [
              {
                "id": "3", // Use string ID
                "stripNumber": "44",
                "replaceNumber": "353",
                "minDigits": 2,
                "maxDigits": 15,
                "stripTypeOfNumber": TypeOfNumber.INTERNATIONAL,
                "stripNumberPlanIndicator": NumberPlanIndicator.ISDN
              }
            ],
            "createdAt": "2023-05-15T10:30:00Z",
            "updatedAt": "2023-05-15T10:30:00Z"
          };
          console.log(`[DEBUG] Using hardcoded B_NUMBER config:`, bNumberConfig);
          resolve(bNumberConfig);
          return;
        }

        // Always allow access in development mode
        if (process.env.NODE_ENV === 'development' || SecurityService.checkPermission("SMSC_STRIP_AND_REPLACE_READ_PERMISSION")) {
          console.log(`[DEBUG] Permission check passed, getting strip and replace list`);

          // Get all strip and replace configurations and filter by type
          this.getStripAndReplaceList()
            .then((stripAndReplaceList) => {
              console.log(`[DEBUG] Got stripAndReplaceList:`, stripAndReplaceList);
              console.log(`[DEBUG] List length:`, stripAndReplaceList.length);

              // Find the configuration with the matching type
              const matchingConfig = stripAndReplaceList.find(config => {
                console.log(`[DEBUG] Checking config:`, config);
                console.log(`[DEBUG] Config type:`, config.type);
                console.log(`[DEBUG] Looking for type:`, type);
                console.log(`[DEBUG] Match:`, config.type === type);
                return config.type === type;
              });

              console.log(`[DEBUG] Found matching config:`, matchingConfig);

              if (matchingConfig) {
                console.log(`[DEBUG] Found matching StripAndReplace config for type ${type}:`, matchingConfig);
                console.log(`[DEBUG] Entries before processing:`, matchingConfig.stripAndReplaceEntries);

                // Ensure stripAndReplaceEntries is an array
                if (!matchingConfig.stripAndReplaceEntries) {
                  console.log(`[DEBUG] No entries found, creating empty array`);
                  matchingConfig.stripAndReplaceEntries = [];
                }

                // Ensure each entry has an id and it's a string (for drag and drop)
                matchingConfig.stripAndReplaceEntries = matchingConfig.stripAndReplaceEntries
                  .filter(entry => {
                    const valid = entry !== null && entry !== undefined;
                    console.log(`[DEBUG] Entry valid:`, valid, entry);
                    return valid;
                  }) // Filter out null/undefined entries
                  .map((entry, index) => {
                    const processedEntry = {
                      ...entry,
                      // Ensure ID is a string for drag and drop functionality
                      id: entry.id ? String(entry.id) : `temp-${index}`
                    };
                    console.log(`[DEBUG] Processed entry ${index}:`, processedEntry);
                    return processedEntry;
                  });

                console.log(`[DEBUG] Final entries after processing:`, matchingConfig.stripAndReplaceEntries);
                console.log(`[DEBUG] Final entries length:`, matchingConfig.stripAndReplaceEntries.length);

                resolve(matchingConfig);
              } else {
                // If no matching configuration exists, create a new empty one
                console.log(`[DEBUG] No matching StripAndReplace config found for type ${type}, creating new one`);
                const newConfig = {
                  type: type,
                  moStripAndReplaceEnabled: false,
                  aoStripAndReplaceEnabled: false,
                  stripAndReplaceEntries: []
                } as StripAndReplace;

                console.log(`[DEBUG] Created new empty config:`, newConfig);

                // Return the new empty configuration
                resolve(newConfig);
              }
            })
            .catch((error) => {
              console.warn("[DEBUG] Error fetching StripAndReplace list:", error);
              console.warn("[DEBUG] Error message:", error.message);

              // If there's an error fetching the list, return an empty configuration
              const emptyConfig = {
                type: type,
                moStripAndReplaceEnabled: false,
                aoStripAndReplaceEnabled: false,
                stripAndReplaceEntries: []
              } as StripAndReplace;

              console.log(`[DEBUG] Returning empty StripAndReplace config for type ${type} due to error:`, emptyConfig);
              resolve(emptyConfig);
            });
        } else {
          console.error("[DEBUG] SMSC_STRIP_AND_REPLACE_READ_PERMISSION is required.");
          reject(new Error("Permission denied"));
        }
      } catch (error) {
        console.error("[DEBUG] Unexpected error in getStripAndReplaceByType:", error);
        console.error("[DEBUG] Error details:", error);

        // Return an empty configuration in case of unexpected errors
        const fallbackConfig = {
          type: type,
          moStripAndReplaceEnabled: false,
          aoStripAndReplaceEnabled: false,
          stripAndReplaceEntries: []
        } as StripAndReplace;

        console.log(`[DEBUG] Returning fallback config due to unexpected error:`, fallbackConfig);
        resolve(fallbackConfig);
      }
    });
  },

  saveStripAndReplace(stripAndReplace: StripAndReplace): Promise<StripAndReplace> {
    return new Promise((resolve, reject) => {
      try {
        // Always allow access in development mode
        if (process.env.NODE_ENV === 'development' || SecurityService.checkPermission("SMSC_STRIP_AND_REPLACE_WRITE_PERMISSION")) {
          // Check if we have a valid base URL
          const baseUrl = $properties.value["client.smsws.base.url"];
          const url = `${baseUrl}/stripAndReplace${
            stripAndReplace.id ? "/" + stripAndReplace.id : ""
          }`;

          // Always use PUT for updating strip and replace settings
          // The backend expects PUT for both creating and updating
          const method = "put";

          console.log(`Making ${method.toUpperCase()} request to ${url} with data:`, stripAndReplace);

          // Use HttpService instead of direct axios to handle CORS and other issues
          HttpService[method](url, undefined, stripAndReplace)
            .then((response) => {
              if (response.status === 200 || response.status === 201) {
                console.log("StripAndReplace saved successfully:", response.data);
                resolve(response.data);
              } else {
                console.warn("Unexpected status code from stripAndReplace API:", response.status);
                reject(response.statusText);
              }
            })
            .catch((error) => {
              console.error("Error saving stripAndReplace:", error);
              reject(error);
            });
        } else {
          console.error("SMSC_STRIP_AND_REPLACE_WRITE_PERMISSION is required.");
          reject(new Error("Permission denied"));
        }
      } catch (error) {
        console.error("Unexpected error in saveStripAndReplace:", error);
        reject(error);
      }
    });
  },

  saveStripAndReplaceEntry(stripAndReplaceId: string | number, entry: StripAndReplaceEntry): Promise<StripAndReplaceEntry> {
    return new Promise((resolve, reject) => {
      if (process.env.NODE_ENV === 'development' || SecurityService.checkPermission("SMSC_STRIP_AND_REPLACE_WRITE_PERMISSION")) {
        const baseUrl = $properties.value["client.smsws.base.url"];
        const method = entry.id ? "put" : "post";
        const url = `${baseUrl}/stripAndReplace/${stripAndReplaceId}/entries${
          entry.id ? "/" + entry.id : ""
        }`;

        HttpService[method](url, entry)
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_STRIP_AND_REPLACE_WRITE_PERMISSION is required.");
        reject(new Error("Permission denied"));
      }
    });
  },

  deleteStripAndReplaceById(id: string | number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (process.env.NODE_ENV === 'development' || SecurityService.checkPermission("SMSC_STRIP_AND_REPLACE_DELETE_PERMISSION")) {
        const baseUrl = $properties.value["client.smsws.base.url"];
        HttpService.delete(`${baseUrl}/stripAndReplace/${id}`)
          .then((response) => {
            if (response.status === 200 || response.status === 204) {
              resolve(true);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_STRIP_AND_REPLACE_DELETE_PERMISSION is required.");
        reject(new Error("Permission denied"));
      }
    });
  },

  deleteStripAndReplaceEntry(stripAndReplaceId: string | number, entryId: string | number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (process.env.NODE_ENV === 'development' || SecurityService.checkPermission("SMSC_STRIP_AND_REPLACE_WRITE_PERMISSION")) {
        const baseUrl = $properties.value["client.smsws.base.url"];
        HttpService.delete(`${baseUrl}/stripAndReplace/${stripAndReplaceId}/entries/${entryId}`)
          .then((response) => {
            if (response.status === 200 || response.status === 204) {
              resolve(true);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_STRIP_AND_REPLACE_WRITE_PERMISSION is required.");
        reject(new Error("Permission denied"));
      }
    });
  },

  // Alias for saveStripAndReplace to maintain API consistency
  updateStripAndReplace(stripAndReplace: StripAndReplace): Promise<StripAndReplace> {
    return this.saveStripAndReplace(stripAndReplace);
  },
};
