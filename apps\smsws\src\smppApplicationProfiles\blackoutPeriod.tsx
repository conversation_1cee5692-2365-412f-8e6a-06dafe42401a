import {
  Autocomplete,
  Box,
  Button,
  Chip,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  Tab,
  Tabs,
} from "@mui/material";
import TextField from "@mui/material/TextField";
import { DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import { $i18n } from "@pnmui/common/services/propertiesService";
import dayjs, { Dayjs } from "dayjs";
import utc from "dayjs/plugin/utc";
import { ReactNode, SyntheticEvent, useEffect, useState } from "react";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";

dayjs.extend(utc);

interface CustomTabPanelProps {
  children?: ReactNode;
  value: number;
  index: number;
  [key: string]: any;
}

function CustomTabPanel(props: CustomTabPanelProps): JSX.Element {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface BlackoutPeriodData {
  id?: string | number;
  start?: string;
  end?: string;
  startTime?: string;
  endTime?: string;
  weekdays?: string[];
}

interface BlackoutPeriodProps {
  data?: BlackoutPeriodData;
  onSave: (data: BlackoutPeriodData) => void;
  onClose: () => void;
  open: boolean;
}

export default function BlackoutPeriod(props: BlackoutPeriodProps): JSX.Element {
  console.log("BlackoutPeriod props", props);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [tab, setTab] = useState<number>(
    props.data && props.data.weekdays && props.data.weekdays.length ? 1 : 0
  );
  const [start, setStart] = useState<Dayjs>(() => {
    if (props.data && props.data.start) {
      return dayjs.utc(props.data.start);
    }
    return dayjs.utc();
  });

  const [end, setEnd] = useState<Dayjs>(() => {
    if (props.data && props.data.end) {
      return dayjs.utc(props.data.end);
    }
    return dayjs.utc();
  });

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    if (props.data && props.data.start) {
      setStart(dayjs.utc(props.data.start));
    }
    if (props.data && props.data.end) {
      setEnd(dayjs.utc(props.data.end));
    }
  }, [props.data]);

  const weekdayOptions = [
    { label: "Sunday", value: 0 },
    { label: "Monday", value: 1 },
    { label: "Tuesday", value: 2 },
    { label: "Wednesday", value: 3 },
    { label: "Thursday", value: 4 },
    { label: "Friday", value: 5 },
    { label: "Saturday", value: 6 },
  ];
  const [weekdays, setWeekdays] = useState(
    props.data && props.data.weekdays && props.data.weekdays.length
      ? props.data.weekdays.map((w) =>
          weekdayOptions.find((o) => o.value === w)
        )
      : []
  );

  return (
    <Dialog open={props.open}>
      <DialogTitle>
        {
          i18n[
            "sms.blackoutProfile.blackoutPeriod.form.title" || "Blackout Period"
          ]
        }
      </DialogTitle>
      <DialogContent>
        <Tabs value={tab} onChange={(e, v) => setTab(v)}>
          <Tab label={"Fixed"} />
          <Tab label={"Weekly"} />
        </Tabs>
        <CustomTabPanel value={tab} index={0}>
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "start",
              gap: "1em",
            }}
          >
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                label="Start"
                value={start}
                onChange={(date: Dayjs | null) => date && setStart(date)}
                renderInput={(params) => <TextField {...params} />}
              />
            </LocalizationProvider>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                label="End"
                value={end}
                onChange={(date: Dayjs | null) => date && setEnd(date)}
                renderInput={(params) => <TextField {...params} />}
              />
            </LocalizationProvider>
          </div>
        </CustomTabPanel>
        <CustomTabPanel value={tab} index={1}>
          <Autocomplete
            className={"blackoutPeriodAutocomplete"}
            multiple
            renderTags={(tagValue, getTagProps) =>
              tagValue.map((option, index) => {
                const { key, ...tagProps } = getTagProps({ index });
                return <Chip key={key} label={option ? option.label : option} {...tagProps} />;
              })
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label="Weekdays"
                placeholder=""
                style={{ marginTop: "1em", paddingTop: "-1em" }}
              />
            )}
            options={weekdayOptions}
            value={weekdays}
            isOptionEqualToValue={(option, value) =>
              option.value === value ? value.value : value
            }
            onChange={(_e: SyntheticEvent, val: any) => {
              console.log(val);
              setWeekdays(val);
            }}
          />
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "start",
              gap: "1em",
              marginTop: "1em",
            }}
          >
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                sx={{ width: "8em !important", maxWidth: "8em !important", minWidth: "8em !important" }}
                label="Start"
                value={start}
                onChange={(date) => setStart(date)}
                renderInput={(params) => <TextField {...params} />}
              />
            </LocalizationProvider>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                sx={{ width: "8em !important", maxWidth: "8em !important", minWidth: "8em !important" }}
                label="End"
                value={end}
                onChange={(date) => setEnd(date)}
                renderInput={(params) => <TextField {...params} />}
              />
            </LocalizationProvider>
          </div>
        </CustomTabPanel>
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          color="secondary"
          type="button"
          onClick={() => props.onClose()}
        >
          {i18n["button.cancel"] || "Cancel"}
        </Button>
        <Button
          variant="contained"
          color="primary"
          type="button"
          onClick={() =>
            props.onClose({
              start: start.toISOString(),
              end: end.toISOString(),
              weekdays,
            })
          }
          className="request-handler-submit-button"
        >
          {i18n["button.submit"] || "Submit"}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
