import {
  <PERSON><PERSON>,
  <PERSON>,
  CardA<PERSON>,
  CardContent,
  CardHeader,
} from "@mui/material";
import { SecurityService } from "@pnmui/common/services/securityService";
import { useEffect, useState } from "react";
import { TextValidator, ValidatorForm } from "react-material-ui-form-validator";
import { useNavigate, useParams } from "react-router-dom";

import { ErrorsDisplay } from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SmscDeliveryAndRetryProfilesService } from "@pnmui/common/services/smsc/deliveryAndRetryProfilesService";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import { SmscNumberListsService } from "@pnmui/common/services/smsc/numberListsService";
import { SmscResourcePoliciesService } from "@pnmui/common/services/smsc/resourcePoliciesService";
import { SmscApplicationProfilesService } from "@pnmui/common/services/smsc/smppApplicationProfilesService";
import { SmscSpamWordsService } from "@pnmui/common/services/smsc/spamWordsService";
import dayjs from "dayjs";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import "../common.css";
import {
  ApplicationProfile,
  DeliveryAndRetryProfile,
  EnumerationOption,
  NumberList,
  ResourcePolicy,
  SpamWordRegistry,
} from "../types";
import AdvancedAccordion from "./AdvancedAccordion";
import BlackoutsAccordion from "./BlackoutsAccordion";
import CDRsAccordion from "./CDRsAccordion";
import ChargingAccordion from "./ChargingAccordion";
import { handleChange } from "./common";
import ContentScreeningAccordion from "./ContentScreeningAccordion";
import EncodingAccordion from "./EncodingAccordion";
import NumberScreeningAccordion from "./NumberScreeningAccordion";
import ReceiptsAccordion from "./ReceiptsAccordion";
import RetriesAccordion from "./RetriesAccordion";
import StorageAccordion from "./StorageAccordion";
import ThrottlingAccordion from "./ThrottlingAccordion";
import { validateApplicationProfile } from "./validation";

interface Enumerations {
  [key: string]: EnumerationOption[];
}

interface FormErrors {
  [key: string]: string | FormErrors;
}

const ApplicationProfileForm = () => {
  const navigate = useNavigate();
  const params = useParams<{ id: string }>();
  const [applicationProfile, setApplicationProfile] = useState<ApplicationProfile>({} as ApplicationProfile);
  const [enumerations, setEnumerations] = useState<Enumerations>({});
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [numberLists, setNumberLists] = useState<NumberList[]>([]);
  const [deliveryAndRetryProfiles, setDeliveryAndRetryProfiles] = useState<DeliveryAndRetryProfile[]>([]);
  const [spamWordRegistries, setSpamWordRegistries] = useState<SpamWordRegistry[]>([]);
  const [resourcePolicies, setResourcePolicies] = useState<ResourcePolicy[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [errors, setErrors] = useState<FormErrors>({});
  const [errorResponse, setErrorResponse] = useState<any>(null);

  useEffect(() => {
    const loadData = async (): Promise<void> => {
      setIsLoading(true);
      try {
        const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
          if (i18NProps) {
            setI18n(i18NProps);
          }
        });
        const [enums, lists, profiles, words, policies] = await Promise.all([
          SmscEnumerationsService.getEnumerations(),
          SmscNumberListsService.getNumberLists(),
          SmscDeliveryAndRetryProfilesService.getDeliveryAndRetryProfiles(),
          SmscSpamWordsService.getSpamWords(),
          SmscResourcePoliciesService.getResourcePolicies()
        ]);

        setEnumerations(enums);
        setNumberLists(lists);
        setDeliveryAndRetryProfiles(profiles);
        setSpamWordRegistries(words);
        setResourcePolicies(policies);

        if (params.id && params.id !== "new") {
          const data = await SmscApplicationProfilesService.getSmppApplicationProfileById(
            params.id
          );
          console.log("Application Profile data", data);
          setApplicationProfile(convertFromWebServiceObject(data));
        }

        return () => subscription.unsubscribe();
      } catch (error: any) {
        console.error("Error loading data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [params.id]);

  useEffect(() => {
    console.log("ApplicationProfile changed:", applicationProfile);
  }, [applicationProfile]);

  const convertFromWebServiceObject = (input: any): ApplicationProfile => {
    const result = {
      id: input.id,
      name: input.name,
      encoding: {
        defaultAlphabetForReceivedSMS: "NONE",
        defaultAlphabetForDeliveryOfSMS: "NONE",
      },
      retries: {
        enabled: false,
        overrideMessagePriority: false,
        priorityIfNoneSpecified: "",
        alwaysUsePriority: "",
        lowPriorityDeliveryAndRetryProfile: null,
        regularPriorityDeliveryAndRetryProfile: null,
        urgentPriorityDeliveryAndRetryProfile: null,
        emergencyPriorityDeliveryAndRetryProfile: null,
      },
      charging: {
        enabled: false,
        changeOriginatingSubscriber: false,
        whenToCharge: null,
      },
      storage: {
        enabled: input.storageProfile?.enabled ?? false,
        enableMessageReassembly: false,
        storageResourcePolicy: null,
        consumerResourcePolicy: null,
      },
      receipts: {
        enabled: false,
        receiptResourcePolicy: null,
        receiptDeliveryAndRetryProfile: null,
      },
      cdrs: {
        enabled: false,
        submitCDR: false,
        retryCDR: false,
        chargingCDR: false,
        finalCDR: false,
        receiptCDR: false,
        auxiliaryServiceCDR: false,
      },
      contentScreening: {
        enabled: false,
        spamWordRegistry: null,
        severityThreshold: 5,
      },
      numberScreening: {
        enabled: input.numberScreeningProfile?.enabled ?? false
      },
      throttling: {
        enabled: false,
        maxMessages: 100,
        timeWindow: 60,
        analysisWindow: 30,
        accuracy: 95,
      },
      blackouts: {
        enabled: false,
        periods: [],
      },
      advanced: {
        enabled: input.additionalProperties && input.additionalProperties.length > 0 ? true : false,
        properties: []
      },
    };

    if (input.additionalProperties) {
      result.advanced.properties = input.additionalProperties.map((prop) => ({
        id: prop.id,
        propertyName: prop.name,
        propertyValue: prop.value,
        section: prop.section
      }));
    }

    if (input.dataCodingProfile) {
      result.encoding = {
        ...result.encoding,
        defaultAlphabetForReceivedSMS: input.dataCodingProfile.defaultStandardForReceivedSms || "NONE"
      };
    }
    if (input.deliverEncodingProfile) {
      if (!result.encoding) {
        result.encoding = {};
      }
      result.encoding.defaultAlphabetForDeliveryOfSMS = input.deliverEncodingProfile.defaultStandardForDeliveryOfSms || "NONE";
    } else if (input.dataCodingProfile && input.dataCodingProfile.defaultStandardForDeliveryOfSms) {
      // Fallback to dataCodingProfile if deliverEncodingProfile is not available
      if (!result.encoding) {
        result.encoding = {};
      }
      result.encoding.defaultAlphabetForDeliveryOfSMS =
        input.dataCodingProfile.defaultStandardForDeliveryOfSms || "NONE";
    }

    if (input.priorityRetryProfile) {
      result.retries = {
        enabled: true,
        overrideMessagePriority: input.priorityRetryProfile.overrideMessagePriority,
        priorityIfNoneSpecified: input.priorityRetryProfile.defaultPriority,
        alwaysUsePriority: input.priorityRetryProfile.overridePriority || "",
        lowPriorityDeliveryAndRetryProfile:
          input.priorityRetryProfile.lowPriorityDeliveryAndRetryProfile,
        regularPriorityDeliveryAndRetryProfile:
          input.priorityRetryProfile.regularPriorityDeliveryAndRetryProfile,
        urgentPriorityDeliveryAndRetryProfile:
          input.priorityRetryProfile.urgentPriorityDeliveryAndRetryProfile,
        emergencyPriorityDeliveryAndRetryProfile:
          input.priorityRetryProfile.emergencyPriorityDeliveryAndRetryProfile,
      };
    }

    if (input.chargingProfile) {
      result.charging = {
        enabled: input.chargingProfile.enabled,
        changeOriginatingSubscriber:
          !!input.chargingProfile.originatingSubscriber,
        whenToCharge: input.chargingProfile.chargingStrategy,
      };
    }

    if (input.storageProfile) {
      result.storage = {
        enabled: input.storageProfile.enabled ?? false,
        enableMessageReassembly: input.storageProfile.enableMessageReassembly ?? false,
        storageResourcePolicy: input.storageProfile.storeResourcePolicy ?? null,
        consumerResourcePolicy: input.storageProfile.consumerResourcePolicy ?? null,
      };
    }

    if (input.receiptStorageProfile) {
      result.receiptStorage = {
        enabled: input.receiptStorageProfile.enabled,
        enableMessageReassembly:
          input.receiptStorageProfile.enableMessageReassembly,
        storageResourcePolicy: input.receiptStorageProfile.storeResourcePolicy,
        consumerResourcePolicy:
          input.receiptStorageProfile.consumerResourcePolicy,
      };
    }

    if (input.receiptProfile) {
      result.receipts = {
        enabled: input.receiptProfile.enabled,
        receiptResourcePolicy: input.receiptProfile.receiptResourcePolicy,
        receiptDeliveryAndRetryProfile:
          input.receiptProfile.receiptDeliveryAndRetryProfile,
      };
    }

    if (input.cdrProfile) {
      result.cdrs = {
        enabled: input.cdrProfile.enabled,
        submitCDR: input.cdrProfile.submitCdr,
        retryCDR: input.cdrProfile.retryCdr,
        chargingCDR: input.cdrProfile.chargingCdr,
        finalCDR: input.cdrProfile.finalCdr,
        receiptCDR: input.cdrProfile.receiptCdr,
        auxiliaryServiceCDR: input.cdrProfile.auxServicesCdr,
      };
    }

    if (input.contentScreeningProfile) {
      result.contentScreening = {
        enabled: input.contentScreeningProfile.enabled,
        spamWordRegistry: input.contentScreeningProfile.spamWordRegistry,
        severityThreshold: input.contentScreeningProfile.tokenThreshold,
      };
    }

    if (input.numberScreeningProfile) {
      result.numberScreening = {
        enabled: input.numberScreeningProfile?.enabled ?? false,
        aNumberAllowList: Array.isArray(input.numberScreeningProfile?.anumberWhiteList)
          ? input.numberScreeningProfile?.anumberWhiteList[0]
          : input.numberScreeningProfile?.anumberWhiteList || null,
        aNumberBlockList: Array.isArray(input.numberScreeningProfile?.anumberBlackList)
          ? input.numberScreeningProfile?.anumberBlackList[0]
          : input.numberScreeningProfile?.anumberBlackList || null,
        bNumberAllowList: Array.isArray(input.numberScreeningProfile?.bnumberWhiteList)
          ? input.numberScreeningProfile?.bnumberWhiteList[0]
          : input.numberScreeningProfile?.bnumberWhiteList || null,
        bNumberBlockList: Array.isArray(input.numberScreeningProfile?.bnumberBlackList)
          ? input.numberScreeningProfile?.bnumberBlackList[0]
          : input.numberScreeningProfile?.bnumberBlackList || null,
        vlrAllowList: Array.isArray(input.numberScreeningProfile?.vlrWhiteList)
          ? input.numberScreeningProfile?.vlrWhiteList[0]
          : input.numberScreeningProfile?.vlrWhiteList || null,
        vlrBlockList: Array.isArray(input.numberScreeningProfile?.vlrBlackList)
          ? input.numberScreeningProfile?.vlrBlackList[0]
          : input.numberScreeningProfile?.vlrBlackList || null
      };
    }

    if (input.throttlingProfile) {
      result.throttling = {
        enabled: input.throttlingProfile.enabled,
        maxMessages: input.throttlingProfile.analysisWindowSMSThreshold,
        timeWindow: input.throttlingProfile.analysisWindowInSeconds,
        analysisWindow: input.throttlingProfile.numberOfWindowsToAnalyse,
        accuracy: input.throttlingProfile.analysisWindowThreshold,
      };
    }

    if (input.blackoutProfile) {
      result.blackouts = {
        enabled: input.blackoutProfile.enabled,
        periods: input.blackoutProfile.blackoutPeriods.map((period) => ({
          id: period.id,
          name: period.name,
          start: period.duration.startTime,
          startTime: period.duration.startTime,
          end: period.duration.endTime,
          endTime: period.duration.endTime,
          startDate: period.duration.startDate,
          endDate: period.duration.endDate,
          weekdays: period.duration.daysActive
            ? period.duration.daysActive.split(",").map(Number)
            : undefined,
        })),
      };
    }
    console.log("convertFromWebServiceObject result", result);
    return result;
  };

  const convertToWebServiceObject = (input: ApplicationProfile): any => {
    console.log("input", input);

    const result = {
      name: input.name || "",
    };

    if (input.id) {
      result.id = input.id;
    }

    result.additionalProperties =
      input.advanced?.properties?.map((prop) => {
        const mappedProp = {
          name: prop.propertyName || "",
          value: prop.propertyValue || "",
          section: typeof prop.section === "object" ? prop.section.value || prop.section.name : prop.section || ""
        };

        // Only include the ID if it's not a temporary one (doesn't start with temp_)
        if (prop.id && !prop.id.toString().startsWith('temp_')) {
          mappedProp.id = prop.id;
        }

        return mappedProp;
      }) || [];

    // Encoding Profile
    result.dataCodingProfile = {
      defaultStandardForReceivedSms: input.encoding?.defaultAlphabetForReceivedSMS === "NONE" ? null : input.encoding?.defaultAlphabetForReceivedSMS || null
    };

    result.deliverEncodingProfile = {
      defaultStandardForDeliveryOfSms: input.encoding?.defaultAlphabetForDeliveryOfSMS === "NONE" ? null : input.encoding?.defaultAlphabetForDeliveryOfSMS || null
    };

    // Priority Retry Profile
    const retries = input.retries || {};
    result.priorityRetryProfile = {
      overrideMessagePriority: retries.overrideMessagePriority || false,
      overridePriority: retries.alwaysUsePriority || null
    };
    if (
      result.priorityRetryProfile.overrideMessagePriority &&
      result.priorityRetryProfile.overridePriority
    ) {
      const profileId =
        input.retries[
          `${result.priorityRetryProfile.overridePriority.toLowerCase()}PriorityDeliveryAndRetryProfile`
        ] || null;
        result.priorityRetryProfile[
          `${result.priorityRetryProfile.overridePriority.toLowerCase()}PriorityDeliveryAndRetryProfile`
        ] = profileId;
    } else {
      result.priorityRetryProfile.defaultPriority =
      input.retries?.priorityIfNoneSpecified || 'LOW';
      result.priorityRetryProfile.lowPriorityDeliveryAndRetryProfile =
        input.retries?.lowPriorityDeliveryAndRetryProfile || null;
      result.priorityRetryProfile.regularPriorityDeliveryAndRetryProfile =
        input.retries?.regularPriorityDeliveryAndRetryProfile || null;
      result.priorityRetryProfile.urgentPriorityDeliveryAndRetryProfile =
        input.retries?.urgentPriorityDeliveryAndRetryProfile || null;
      result.priorityRetryProfile.emergencyPriorityDeliveryAndRetryProfile =
        input.retries?.emergencyPriorityDeliveryAndRetryProfile || null;
    }

    // Charging Profile
    result.chargingProfile = {
      enabled: input.charging?.enabled || false,
    };
    if (result.chargingProfile.enabled) {
      result.chargingProfile.originatingSubscriber =
        input.charging?.changeOriginatingSubscriber || false;
      result.chargingProfile.chargingStrategy =
        input.charging?.whenToCharge || "CHARGE_BEFORE_STORE";
    }

    // Storage Profile
    result.storageProfile = {
      enabled: input.storage?.enabled || false,
    };
    if (result.storageProfile.enabled) {
      result.storageProfile.enableMessageReassembly =
        input.storage?.enableMessageReassembly || false;
      result.storageProfile.storeResourcePolicy =
        input.storage?.storageResourcePolicy || null;
      result.storageProfile.consumerResourcePolicy =
        input.storage?.consumerResourcePolicy || null;
    }
    result.receiptStorageProfile = {
      enabled: input.receiptStorage?.enabled || false,
    };
    if (result.receiptStorageProfile.enabled) {
      result.receiptStorageProfile.enableMessageReassembly =
        input.receiptStorage?.enableMessageReassembly || false;
      result.receiptStorageProfile.storeResourcePolicy =
        input.receiptStorage?.storageResourcePolicy || null;
      result.receiptStorageProfile.consumerResourcePolicy =
        input.receiptStorage?.consumerResourcePolicy || null;
    }

    // Receipt Profile
    result.receiptProfile = {
      enabled: input.receipts?.enabled || false,
      receiptResourcePolicy: input.receipts?.receiptResourcePolicy || null,
      receiptDeliveryAndRetryProfile:
        input.receipts?.receiptDeliveryAndRetryProfile?.id ||
        input.receipts?.receiptDeliveryAndRetryProfile ||
        null,
      receiptSubscriberOnSuccessEnabled: false,
      receiptSubscriberOnFailureEnabled: false,
      receiptForcedEnabled: false,
    };

    // CDR Profile
    result.cdrProfile = {
      enabled: input.cdrs?.enabled || false,
    };
    if (result.cdrProfile && result.cdrProfile.enabled) {
      result.cdrProfile.submitCdr = input.cdrs?.submitCDR || false;
      result.cdrProfile.retryCdr = input.cdrs?.retryCDR || false;
      result.cdrProfile.chargingCdr = input.cdrs?.chargingCDR || false;
      result.cdrProfile.finalCdr = input.cdrs?.finalCDR || false;
      result.cdrProfile.receiptCdr = input.cdrs?.receiptCDR || false;
      result.cdrProfile.auxServicesCdr =
        input.cdrs?.auxiliaryServiceCDR || false;
    }

    // Content Screening Profile
    result.contentScreeningProfile = {
      enabled: input.contentScreening?.enabled || false,
    };
    if (result.contentScreeningProfile.enabled) {
      result.contentScreeningProfile.spamWordRegistry =
        input.contentScreening?.spamWordRegistry || null;
      result.contentScreeningProfile.tokenThreshold =
        input.contentScreening?.severityThreshold || 100;
    }

    // Throttling Profile
    result.throttlingProfile = {
      enabled: input.throttling?.enabled || false,
    };
    if (result.throttlingProfile.enabled) {
      result.throttlingProfile.analysisWindowInSeconds =
        input.throttling?.timeWindow || 60;
      result.throttlingProfile.analysisWindowSMSThreshold =
        input.throttling?.maxMessages || 100;
      result.throttlingProfile.numberOfWindowsToAnalyse =
        input.throttling?.analysisWindow || 5;
      result.throttlingProfile.analysisWindowThreshold =
        input.throttling?.accuracy || 95;
    }

    result.blackoutProfile = {
      enabled: input.blackouts?.enabled || false,
    };

    if (input.blackouts?.enabled) {

      result.blackoutProfile = {
        enabled: input.blackouts.enabled,
        blackoutPeriods: input.blackouts && input.blackouts.periods ? input.blackouts.periods.map(
          (period) => ({
            // Only include id if it's not temporary
            ...(period.id && (typeof period.id != 'string' || !period.id.startsWith('temp_')) ? { id: period.id } : {}),
            name: period.name || "",
            duration: {
              type: period.weekdays && period.weekdays.length > 0
                ? "weeklyBlackoutDuration"
                : "fixedBlackoutDuration",
              startTime:
                period.start.indexOf("T") !== -1
                  ? dayjs(period.start).format("HH:mm:ss")
                  : period.start || null,
              endTime:
                period.end.indexOf("T") !== -1
                  ? dayjs(period.end).format("HH:mm:ss")
                  : period.end || null,
              startDate:
                period.startDate && period.startDate.indexOf("T") !== -1
                  ? dayjs(period.startDate).format("YYYY/MM/DD")
                  : period.startDate || undefined,
              endDate:
                period.endDate && period.endDate.indexOf("T") !== -1
                  ? dayjs(period.endDate).format("YYYY/MM/DD")
                  : period.endDate || undefined,
              daysActive:
                period.weekdays && period.weekdays.length
                  ? period.weekdays.map((w) => w.value || w).join(",")
                  : '',
            },
          })
        ) : [],
      };
    }

    result.numberScreeningProfile = {
      enabled: input.numberScreening?.enabled || false,
    };
    // Number Screening Profile
    if (input.numberScreening?.enabled) {
      result.numberScreeningProfile = {
        enabled: input.numberScreening.enabled || false,
        anumberWhiteList: input.numberScreening.aNumberAllowList || null,
        anumberBlackList: input.numberScreening.aNumberBlockList || null,
        bnumberWhiteList: input.numberScreening.bNumberAllowList || null,
        bnumberBlackList: input.numberScreening.bNumberBlockList || null,
        vlrWhiteList: input.numberScreening.vlrAllowList || null,
        vlrBlackList: input.numberScreening.vlrBlockList || null,
      };
    }


    console.log("convertToWebServiceObject result", result);
    return result;
  };

  const handleSubmit = (): void => {
    const validationResult = validateApplicationProfile(applicationProfile, enumerations);

    if (validationResult.hasErrors) {
      setErrors(prevErrors => ({
        ...prevErrors,
        ...validationResult.errors
      }));

      // Flatten nested error objects and extract messages
      const flattenErrors = (obj, prefix = '') => {
        return Object.entries(obj).reduce((acc, [key, value]) => {
          if (value && typeof value === 'object' && !Array.isArray(value)) {
            return [...acc, ...flattenErrors(value, `${prefix}${key}.`)];
          }
          return [...acc, `${value}`];
        }, []);
      };

      const errorMessages = flattenErrors(validationResult.errors);
      toast.error(<div>
        {errorMessages.map((message, index) => (
          <div key={index}>{message}</div>
        ))}
      </div>);
      return;
    }

    SmscApplicationProfilesService.saveSmppApplicationProfile(
      convertToWebServiceObject(applicationProfile)
    )
      .then((data) => {
        console.log("handleSubmit data", data);
        toast.success("Application Profile saved successfully");
        navigate("/smscApplicationProfiles");
      })
      .catch((error: any) => {
        console.error("handleSubmit error", error);
        setErrorResponse(error);
      });
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div style={{ padding: "0.5em", paddingTop: "2em" }}>
      <ValidatorForm
        onSubmit={handleSubmit}
        className="tango-form"
      >
        <Card>
          <CardContent>
            <ErrorsDisplay
              errorResponse={errorResponse}
              keyPrefix={"smsws.application.profile.form.keys"}
            />
            <TextValidator
              label="Name"
              onChange={(e) => handleChange(e, setApplicationProfile)}
              name="name"
              value={applicationProfile.name || ""}
              validators={["required"]}
              errorMessages={["Name is required"]}
            />
            <EncodingAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              enumerations={enumerations}
            />
            <RetriesAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              enumerations={enumerations}
              deliveryAndRetryProfiles={deliveryAndRetryProfiles}
              setDeliveryAndRetryProfiles={setDeliveryAndRetryProfiles}
              errors={errors}
            />

            <ChargingAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              enumerations={enumerations}
              errors={errors}
            />
            <StorageAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              resourcePolicies={resourcePolicies}
              errors={errors}
            />
            <ReceiptsAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              resourcePolicies={resourcePolicies}
              deliveryAndRetryProfiles={deliveryAndRetryProfiles}
              setDeliveryAndRetryProfiles={setDeliveryAndRetryProfiles}
              errors={errors}
            />
            <CDRsAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
            />

            <ContentScreeningAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              spamWordRegistries={spamWordRegistries}
              setSpamWordRegistries={setSpamWordRegistries}
              errors={errors}
            />

            <NumberScreeningAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              numberLists={numberLists}
              setNumberLists={setNumberLists}
              errors={errors}
            />
            <ThrottlingAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              errors={errors}
            />
            <BlackoutsAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              errors={errors}
              setErrors={setErrors}
            />
            <AdvancedAccordion
              applicationProfile={applicationProfile}
              setApplicationProfile={setApplicationProfile}
              errors={errors}
              setErrors={setErrors}
            />
          </CardContent>
          <CardActions className="card-actions content-card-actions">
            <Button
              variant="contained"
              color="secondary"
              onClick={() => {
                navigate(-1);
              }}
            >
              {i18n["button.cancel"] || "Cancel"}
            </Button>
            {/* <Button variant="contained" color="primary" type="submit">
              {i18n["button.submit"] || "Submit"}
            </Button> */}
            {SecurityService.checkPermission(
              "SMSC_APPLICATION_PROFILE_UPDATE_PERMISSION"
            ) && (
              <Button
                variant="contained"
                color="primary"
                type="submit"
                className="request-handler-submit-button"
              >
                {i18n["button.submit"] || "Submit"}
              </Button>
            )}
          </CardActions>
        </Card>
      </ValidatorForm>
    </div>
  );
};

export default ApplicationProfileForm;





