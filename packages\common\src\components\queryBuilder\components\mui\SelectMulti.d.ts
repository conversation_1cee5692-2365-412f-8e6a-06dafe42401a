import { ValueSelectorProps } from "../../types";
interface SelectOption {
    value: string;
    label: string;
}
interface SelectMultiProps extends ValueSelectorProps {
    selectedValue: string[];
    values: SelectOption[];
    onChange: (value: string) => void;
    onDelete: (value: string) => void;
}
export declare function SelectMulti({ onChange, onDelete, selectedValue, values }: SelectMultiProps): import("react/jsx-runtime").JSX.Element;
export {};
