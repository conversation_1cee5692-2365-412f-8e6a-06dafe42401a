import { FC, useEffect, useState, SyntheticEvent } from "react";
import { Autocomplete, TextField, AutocompleteRenderInputParams } from "@mui/material";

interface Option {
  name?: string;
  [key: string]: any;
}

interface SearchFieldProps {
  fieldName: string;
  placeHolder?: string;
  onSearch: (value: string | null, fieldName: string) => void;
  fieldType?: string;
  options?: Option[];
}

export const SearchField: FC<SearchFieldProps> = (props) => {
  const [fieldValue, setFieldValue] = useState<string | null>(null);
  const [options, setOptions] = useState<Option[]>([]);

  useEffect(() => {
    setOptions(props.options || []);
    if (fieldValue !== null) {
      props.onSearch(fieldValue, props.fieldName);
    }
  }, [fieldValue, props, props.options]);

  return (
    <div
      style={{
        minWidth: "23%",
        maxWidth: "48%",
        width: "15em",
        marginLeft: "1em",
        display: "inline-block",
      }}
    >
      <Autocomplete
        autoComplete
        freeSolo={props.fieldType !== "select"}
        options={options}
        getOptionLabel={(option: string | Option) => 
          typeof option === 'string' ? option : option.name || ''
        }
        className="searchInput"
        onKeyUp={(e: React.KeyboardEvent) => {
          if (e.which === 13) {
            const target = e.target as HTMLInputElement;
            setFieldValue(target ? target.value : "");
            props.onSearch(fieldValue, props.fieldName);
          }
        }}
        onChangeCapture={(e: React.SyntheticEvent) => {
          const target = e.target as HTMLInputElement;
          setFieldValue(target ? target.value : "");
        }}
        onInputChange={(e: SyntheticEvent) => {
          const target = e.target as HTMLInputElement;
          setFieldValue(target ? target.value : "");
        }}
        onChange={(_e: SyntheticEvent, val: string | Option | null) => {
          if (val === null) {
            setFieldValue("");
          } else if (typeof val === 'string') {
            setFieldValue(val);
          } else {
            setFieldValue(val.name || "");
          }
        }}
        renderInput={(params: AutocompleteRenderInputParams) => (
          <TextField
            {...params}
            className="inputAutocomplete"
            label={props.placeHolder || "Search"}
            variant="outlined"
            inputProps={{
              ...params.inputProps,
              autoComplete: "new-password", // disable autocomplete and autofill
            }}
            margin="dense"
          />
        )}
      />
    </div>
  );
};
