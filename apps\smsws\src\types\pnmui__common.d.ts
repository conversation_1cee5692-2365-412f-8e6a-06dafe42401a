declare module '@pnmui/common' {
  import { Observable } from 'rxjs';
  import * as React from 'react';

  // Common services
  export const $i18n: Observable<Record<string, string>>;
  export const SecurityService: any;

  // Common components
  export interface MaterialTableProps {
    columns: any[];
    data: any[] | ((query: any) => Promise<any>);
    title?: string;
    actions?: any[];
    options?: any;
    onRowClick?: (event: any, rowData: any) => void;
    editable?: any;
    detailPanel?: any;
    [key: string]: any;
  }
  
  export const MaterialTable: React.ComponentType<MaterialTableProps>;
  export const tableIcons: Record<string, React.ReactElement>;
  export const CommandCell: React.ComponentType<any>;
  export const InputCell: React.ComponentType<any>;
  export const SelectCell: React.ComponentType<any>;
  export const DurationCell: React.ComponentType<any>;

  // Services
  export namespace SmscSpamWordsService {
    export function getSpamWords(): Promise<any[]>;
    export function saveSpamWordRegistry(data: any): Promise<any>;
    export function saveSpamWordRegistryWithName(data: any): Promise<any>;
    export function deleteSpamWordRegistry(id: string | number): Promise<any>;
  }

  export namespace SmscNumberListsService {
    export function saveNumberListWithName(data: any): Promise<any>;
  }

  export namespace SmscDeliveryAndRetryProfilesService {
    export function saveDeliveryAndRetryProfileWithName(data: any): Promise<any>;
    export function saveRetryProfileWithName(name: string): Promise<any>;
  }

  // Utils
  export function paginate(array: any[], page: number, pageSize: number): any[];
}
