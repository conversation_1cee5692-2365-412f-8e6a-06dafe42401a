import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, MenuItem } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscDeliveryAndRetrySchedulesService } from "@pnmui/common/services/smsc/deliveryAndRetrySchedulesService";
import { paginate } from "@pnmui/common/utils";
import React, { useEffect, useState } from "react";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import { DeliveryAndRetrySchedule } from "../types";

interface TableData {
  data: DeliveryAndRetrySchedule[];
  page: number;
  totalCount: number;
}

function SmscDeliveryAndRetrySchedules() {
  const [originalProfiles, setOriginalProfiles] = useState<
    DeliveryAndRetrySchedule[]
  >([]);
  const [deliveryAndRetrySchedules, setdeliveryAndRetrySchedules] = useState<
    DeliveryAndRetrySchedule[]
  >([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const navigate = useNavigate();
  const tableRefProfiles = React.createRef<any>();

  useEffect(() => {
    const subscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });
    populateProfiles();

    return () => subscription.unsubscribe();
  }, []);

  async function populateProfiles(): Promise<void> {
    try {
      const profilesResponse =
        await SmscDeliveryAndRetrySchedulesService.getDeliveryAndRetrySchedules();
      const profilesList: DeliveryAndRetrySchedule[] = Array.isArray(
        profilesResponse
      )
        ? profilesResponse
        : [];
      setdeliveryAndRetrySchedules(profilesList);
      setOriginalProfiles(profilesList);
    } catch (error) {
      console.error("Error fetching deliveryAndRetrySchedules:", error);
      toast.error(
        i18n["smsc.deliveryAndRetrySchedules.error.fetch"] ||
          "Error fetching delivery and retry schedules."
      );
    }
  }

  async function populateProfilesData(query: {
    page: number;
    pageSize: number;
  }): Promise<TableData> {
    const paginatedList = paginate(
      deliveryAndRetrySchedules,
      query.page,
      query.pageSize
    );
    return {
      data: paginatedList,
      page: query.page,
      totalCount: deliveryAndRetrySchedules.length,
    };
  }

  const handleRowSelection = (rows: DeliveryAndRetrySchedule[]): void => {
    setSelectedRows(rows.map((row) => row.id || ""));
  };

  const handleDelete = async (): Promise<void> => {
    if (selectedRows.length === 0) {
      console.error("No rows selected for deletion.");
      toast.error(
        i18n["smsc.deliveryAndRetrySchedules.error.noRowsSelected"] ||
          "No rows selected for deletion."
      );
      return;
    }
    if (
      window.confirm(
        i18n["smsc.deliveryAndRetrySchedules.confirm.delete"] ||
          "Are you sure you want to delete?"
      )
    ) {
      try {
        await Promise.all(
          selectedRows.map(async (id) => {
            return await SmscDeliveryAndRetrySchedulesService.deleteRetryScheduleById(
              id
            );
          })
        );
        const updatedProfiles = deliveryAndRetrySchedules.filter(
          (profile) =>
            profile.id !== undefined && selectedRows.indexOf(profile.id) === -1
        );
        setdeliveryAndRetrySchedules([...updatedProfiles]);
        setSelectedRows([]);
        toast.success(
          i18n["smsc.deliveryAndRetrySchedules.toast.deleteSuccess"] ||
            "Rows deleted successfully!"
        );
      } catch (error: any) {
        console.error(error);
        toast.error(
          i18n["smsc.deliveryAndRetrySchedules.toast.deleteFailure"] ||
            error.message
        );
      }
    }
  };

  useEffect(() => {
    tableRefProfiles.current && tableRefProfiles.current.onQueryChange();
  }, [deliveryAndRetrySchedules]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search(): void {
    const filteredProfiles = originalProfiles.filter((profile) =>
      profile.name?.toLowerCase().includes(selectedName.toLowerCase())
    );
    setdeliveryAndRetrySchedules(filteredProfiles);
  }

  return (
    <div className="wrapper">
      <Card className="content-card">
        <div className="form-row">
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>
              <ValidatorForm onSubmit={() => {}}>
                <SelectValidator
                  label={
                    i18n[
                      "smsc.deliveryAndRetrySchedules.retryScheduleList.name"
                    ] || "Name"
                  }
                  margin="normal"
                  variant="outlined"
                  style={{ minWidth: "15em", marginRight: "1rem" }}
                  children={[
                    i18n[
                      "smsc.deliveryAndRetrySchedules.retryScheduleList.any"
                    ] || "Any",
                    ...deliveryAndRetrySchedules.map((c) => c.name),
                  ].map((name) => (
                    <MenuItem key={name} value={name === "Any" ? "" : name}>
                      {name}
                    </MenuItem>
                  ))}
                  onChange={(e) => setSelectedName(e.target.value)}
                  value={selectedName}
                />
              </ValidatorForm>
            </div>
          </div>
        </div>
        <CardContent>
          <MaterialTable
            tableRef={tableRefProfiles}
            icons={tableIcons}
            data={populateProfilesData}
            columns={[
              {
                field: "name",
                title:
                  i18n["smsc.deliveryAndRetrySchedules.table.name"] || "Name",
              },
              {
                title:
                  i18n["smsc.deliveryAndRetrySchedules.table.numberOfStates"] ||
                  "Number of States",
                render: (rowData) =>
                  rowData.states ? rowData.states.length : 0,
              },
              {
                title:
                  i18n["smsc.deliveryAndRetrySchedules.table.state1"] ||
                  "State 1",
                render: (rowData) =>
                  rowData.states && rowData.states[0]
                    ? rowData.states[0].name
                    : "",
              },
              {
                title:
                  i18n["smsc.deliveryAndRetrySchedules.table.state2"] ||
                  "State 2",
                render: (rowData) =>
                  rowData.states && rowData.states[1]
                    ? rowData.states[1].name
                    : "",
              },
              {
                title:
                  i18n["smsc.deliveryAndRetrySchedules.table.state3"] ||
                  "State 3",
                render: (rowData) =>
                  rowData.states && rowData.states[2]
                    ? rowData.states[2].name
                    : "",
              },
              {
                title:
                  i18n["smsc.deliveryAndRetrySchedules.table.state4"] ||
                  "State 4",
                render: (rowData) =>
                  rowData.states && rowData.states[3]
                    ? rowData.states[3].name
                    : "",
              },
              {
                title:
                  i18n["smsc.deliveryAndRetrySchedules.table.state5"] ||
                  "State 5",
                render: (rowData) =>
                  rowData.states && rowData.states[4]
                    ? rowData.states[4].name
                    : "",
              },
            ]}
            options={{
              selection: true,
              actionsColumnIndex: -1,
              toolbar: false,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              emptyRowsWhenPaging: false,
              headerStyle: { fontWeight: "bold" },
            }}
            onRowClick={(event, rowData) => {
              navigate(`/smscDeliveryAndRetryScheduleForm/${rowData.id}`);
            }}
            onSelectionChange={(rows) => handleRowSelection(rows)}
          />
        </CardContent>
        <Button
          variant="contained"
          color="primary"
          className="request-handler-add-button"
          aria-label={i18n["button.add"] || "Add"}
          onClick={() => navigate("/smscDeliveryAndRetryScheduleForm/new")}
          style={{
            marginLeft: "15px",
            marginTop: "2rem",
            marginBottom: "1rem",
          }}
          disabled={
            !SecurityService.checkPermission(
              "SMSC_DELIVERY_RETRY_SCHEDULES_CREATE_PERMISSION"
            )
          }
        >
          {i18n["button.add"] || "Add"}
        </Button>
        <span style={{ marginLeft: "10px" }}>
          <Button
            variant="contained"
            color="secondary"
            type="button"
            onClick={handleDelete}
            style={{ marginTop: "2rem", marginBottom: "1rem" }}
            className="request-handler-add-button"
            disabled={
              !SecurityService.checkPermission(
                "SMSC_DELIVERY_RETRY_SCHEDULES_DELETE_PERMISSION"
              )
            }
          >
            {i18n["button.delete"] || "Delete"}
          </Button>
        </span>
      </Card>
    </div>
  );
}

export default SmscDeliveryAndRetrySchedules;
