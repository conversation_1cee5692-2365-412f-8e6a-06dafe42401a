import { MenuItem, TextField } from "@mui/material";
import React, { FC, ReactNode } from "react";

// Define a type that mimics the TextValidatorProps
interface TextValidatorProps {
  validators?: string[];
  errorMessages?: string[];
  value: any;
  onChange: (event: any) => void;
  children?: ReactNode;
  select?: boolean;
  [key: string]: any;
}

// Create a component that mimics the TextValidator
const TextValidator: FC<TextValidatorProps> = (props) => {
  const { validators, errorMessages, ...otherProps } = props;
  return <TextField {...otherProps}>{props.children}</TextField>;
};

/**
 * Props for the SafeTextValidator component
 */
interface SafeTextValidatorProps extends TextValidatorProps {
  select?: boolean;
  children?: ReactNode;
}

/**
 * A safer wrapper around TextValidator that handles select property correctly
 * and prevents common errors with children and select properties
 */
const SafeTextValidator: FC<SafeTextValidatorProps> = (props) => {
  // Extract select-related props to handle them separately
  const { select, children, ...otherProps } = props;

  // If this is a select field, ensure we have children
  if (select === true) {
    // Only render as select if we have children to avoid MUI errors
    const hasValidChildren = React.Children.count(children) > 0;

    return (
      <TextValidator
        select={true}
        {...otherProps}
      >
        {hasValidChildren ? children : <MenuItem value="">No options available</MenuItem>}
      </TextValidator>
    );
  }

  // For non-select fields, pass through all props
  return <TextValidator {...otherProps}>{children}</TextValidator>;
};

export default SafeTextValidator;
