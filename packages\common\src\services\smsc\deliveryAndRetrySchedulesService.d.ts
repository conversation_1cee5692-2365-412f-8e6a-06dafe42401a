import { DeliveryAndRetrySchedule } from "@pnmui/common/types/smscTypes";
export declare const SmscDeliveryAndRetrySchedulesService: {
    getDeliveryAndRetrySchedules(): Promise<DeliveryAndRetrySchedule[]>;
    getRetryScheduleById(id: string | number): Promise<DeliveryAndRetrySchedule>;
    saveRetrySchedule(retrySchedule: DeliveryAndRetrySchedule): Promise<DeliveryAndRetrySchedule>;
    deleteRetryScheduleById(id: string | number): Promise<boolean>;
    saveRetryScheduleWithName(retrySchedule: {
        name: string;
    }): Promise<DeliveryAndRetrySchedule>;
};
