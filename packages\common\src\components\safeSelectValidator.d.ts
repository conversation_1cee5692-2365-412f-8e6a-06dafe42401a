import { FC, ReactNode } from "react";
interface SelectValidatorProps {
    validators?: string[];
    errorMessages?: string[];
    value: any;
    onChange: (event: any) => void;
    children?: ReactNode;
    [key: string]: any;
}
/**
 * Props for the SafeSelectValidator component
 */
interface SafeSelectValidatorProps extends SelectValidatorProps {
    children?: ReactNode;
}
/**
 * A safer wrapper around SelectValidator that ensures it always has children
 * to prevent common errors with the SelectValidator component
 */
declare const SafeSelectValidator: FC<SafeSelectValidatorProps>;
export default SafeSelectValidator;
