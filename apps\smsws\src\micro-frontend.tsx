// Import process polyfill first to ensure it's available for all modules
import './processPolyfill'

// Import KendoReact license - this must be imported before any KendoReact components
import './kendoLicense.ts'

// Import KendoReact theme
import '@progress/kendo-theme-material/dist/all.css'

import React from 'react'
import { createTheme, ThemeProvider } from "@mui/material";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./App.css";
import "./common.css";

// Import and initialize services from common package
import { PropertiesService, $properties } from '@pnmui/common/services/propertiesService'
import { SecurityService } from '@pnmui/common/services/securityService'

// Set default properties for micro frontend mode
$properties.next({
  "client.smsws.base.url": "http://localhost:3500",
  "client.pcrf.base.url": "http://localhost:3500",
  "client.spcm.base.url": "http://localhost:3500",
  "client.rte.base.url": "http://localhost:3500/campaign-manager",
  "client.ims.base.url": "http://localhost:3500",
  "client.docstore.base.url": "http://localhost:3500",
  "client.sqs.enabled": "true"
});

// Initialize services
Promise.all([
  PropertiesService.init().catch(error => {
    console.error('micro-frontend.tsx: Error initializing PropertiesService:', error);
    return Promise.resolve(); // Continue despite error
  }),
  SecurityService.init().catch(error => {
    console.error('micro-frontend.tsx: Error initializing SecurityService:', error);
    return Promise.resolve(); // Continue despite error
  })
])
  .then(() => {
    console.log('micro-frontend.tsx: Services initialized successfully')
  })
  .catch(error => {
    console.error('micro-frontend.tsx: Error initializing services:', error)
  })

// Create theme for micro frontend components
const defaultMaterialTheme = createTheme({
  palette: {
    primary: {
      main: "#1976d2",
    },
    secondary: {
      main: "#f50057",
    },
  },
});

// Wrapper component for micro frontend pages
export const MicroFrontendWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider theme={defaultMaterialTheme}>
      <div style={{ padding: '20px' }}>
        {children}
      </div>
      <ToastContainer position="top-right" autoClose={3000} />
    </ThemeProvider>
  );
};

// Export wrapped components for micro frontend consumption
export { default as ApplicationsPage } from './applications/applications';
export { default as ApplicationForm } from './applications/applicationForm';
export { default as ConnectionsPage } from './connections/connections';
export { default as ConnectionForm } from './connections/connectionForm';
export { default as DeliveryAndRetryProfilesPage } from './deliveryAndRetryProfiles/deliveryAndRetryProfiles';
export { default as DeliveryAndRetryProfileForm } from './deliveryAndRetryProfiles/deliveryAndRetryProfileForm';
export { default as DeliveryAndRetrySchedulesPage } from './deliveryAndRetrySchedules/deliveryAndRetrySchedules';
export { default as DeliveryAndRetryScheduleForm } from './deliveryAndRetrySchedules/deliveryAndRetryScheduleForm';
export { default as IpListsPage } from './ipLists/ipLists';
export { default as IpListForm } from './ipLists/ipListsForm';
export { default as NumberListsPage } from './numberLists/numberLists';
export { default as NumberListForm } from './numberLists/numberListForm';
export { default as ResourcePoliciesPage } from './resourcePolicies/resourcePolicies';
export { default as ResourcePolicyForm } from './resourcePolicies/resourcePolicyForm';
export { default as RoutingClassesPage } from './routingClasses/routingClasses';
export { default as RoutingClassForm } from './routingClasses/routingClassForm';
export { default as SmppApplicationProfilesPage } from './smppApplicationProfiles/smppApplicationProfiles';
export { default as SmppApplicationProfileForm } from './smppApplicationProfiles/smppApplicationProfileForm';
export { default as SmsRoutingTablesPage } from './smsRoutingTables/smsRoutingTables';
export { default as SmsRoutingTableForm } from './smsRoutingTables/smsRoutingTableForm';
export { default as SpamWordsPage } from './spamWords/spamWords';
export { default as SpamWordForm } from './spamWords/spamWordForm';
export { default as StripAndReplacePage } from './stripAndReplace/stripAndReplace';
export { default as NumberAnalysisForm } from './numberAnalysis/numberAnalysisForm';
