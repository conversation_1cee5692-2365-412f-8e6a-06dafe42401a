import { toast } from "react-toastify";
import { AxiosError, AxiosResponse } from "axios";

interface SharedQuotaErrors {
  [key: number]: string;
}

interface ErrorReport {
  field: string;
  reason: string;
}

const SHARED_QUOTA_ERRORS: SharedQuotaErrors = {
  1: "An Unexpected Error as occurred",
  2: "Group owner/name pair not unique",
  3: "Group owner can't be a member",
  4: "Total member quota share exceeds 100%",
  5: "Group doesn't exist",
  6: "Max group size exceeded",
  7: "The donor doesn't exist",
  8: "The shareable plan doesn't exist",
  9: "The shareable plan is not a recurring plan",
  10: "Changing group owner not supported",
  11: "Recurring donation for plan already exists",
  12: "The recipient doesn't exist",
};

class ErrorServiceClass {
  spcmSubscriberPropertiesMap: Record<string, string> = {
    paymentType: "Subscriber Type",
    status: "Subscriber State",
  };

  // constructor() {}

  processServerCallError(error: AxiosError | AxiosResponse): Promise<never> {
    console.log("Server Error", error);
    let message: string | string[] = [
      (error as AxiosError).message ||
        ((error as AxiosResponse).data && (error as AxiosResponse).data.message) ||
        ((error as AxiosError).response && (error as AxiosError).response?.data && ((error as AxiosError).response?.data as any)?.message),
    ];

    const axiosError = error as AxiosError;
    if (axiosError.response && axiosError.response.data) {
      const responseData = axiosError.response.data as any;

      if (responseData.data && responseData.data.report) {
        message = responseData.data.report
          .map((err: ErrorReport) => {
            if (err.reason.indexOf("NotNull") !== -1) {
              return `${
                this.spcmSubscriberPropertiesMap[err.field] || err.field
              } is required.`;
            }
            return err.reason;
          })
          .join("<br/>");
      } else if (responseData.message) {
        message = responseData.message;
      } else if (responseData.errorCode) {
        message = SHARED_QUOTA_ERRORS[responseData.errorCode];
      }
    }

    toast.error(message.toString());

    return Promise.reject<never>({
      response: (error as AxiosError).response,
      message,
      error,
    });
  }
}

const ErrorService = new ErrorServiceClass();
export default ErrorService;
