import { FC, ReactNode } from "react";
import "./table.css";
interface TableHeader {
    id?: string;
    path?: string;
    label: string;
    content?: (item: any, data?: any[]) => ReactNode;
}
interface TableItem {
    id?: string | number;
    msisdn?: string;
    externalId?: string;
    [key: string]: any;
}
interface SimpleTableProps {
    headers?: TableHeader[];
    data?: TableItem[];
    hasOrdering?: boolean;
    handleRowClick?: (item: TableItem) => void;
    onDelete?: (selected: (string | number)[]) => void;
    paginationOptions?: {
        pageSizeOptions?: (number | {
            label: string;
            value: number;
        })[];
        totalCount: number;
        pageSize: number;
        onPageChange: (page: number, pageSize: number) => void;
        onRowsPerPageChange: (pageSize: number, page: number) => void;
    };
}
declare const SimpleTable: FC<SimpleTableProps>;
export default SimpleTable;
