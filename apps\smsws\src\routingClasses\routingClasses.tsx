import { <PERSON><PERSON>, <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, MenuItem } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import React, { ChangeEvent, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";

import { SmscRoutingClassesService } from "@pnmui/common/services/smsc/routingClassesService";
import { paginate } from "@pnmui/common/utils";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { RoutingClass } from "../types";

interface TableData {
  data: RoutingClass[];
  page: number;
  totalCount: number;
}

function SmscRoutingClasses() {
  const [originalProfiles, setOriginalProfiles] = useState<RoutingClass[]>([]);
  const [profiles, setProfiles] = useState<RoutingClass[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const navigate = useNavigate();
  const tableRefProfiles = React.createRef<any>();

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });
    populateProfiles();

    return () => subscription.unsubscribe();
  }, []);

  async function populateProfiles(): Promise<void> {
    try {
      const profilesResponse =
        await SmscRoutingClassesService.getRoutingClasses();
      const profilesList: RoutingClass[] = Array.isArray(profilesResponse)
        ? profilesResponse
        : [];
      setProfiles(profilesList);
      setOriginalProfiles(profilesList);
    } catch (error) {
      console.error("Error fetching profiles:", error);
    }
  }

  async function populateProfilesData(query: { page: number; pageSize: number }): Promise<TableData> {
    const paginatedList = paginate(profiles, query.page, query.pageSize);
    return {
      data: paginatedList,
      page: query.page,
      totalCount: profiles.length,
    };
  }

  const handleRowSelection = (rows: RoutingClass[]): void => {
    setSelectedRows(rows.map((row) => row.id || ''));
  };

  const handleDelete = async (): Promise<void> => {
    if (selectedRows.length === 0) {
      console.error("No rows selected for deletion.");
      return;
    }
    if (window.confirm("Are you sure you want to delete?")) {
      try {
        await Promise.all(
          selectedRows.map(async (id) => {
            return await SmscRoutingClassesService.deleteRoutingClassById(id);
          })
        );
        const updatedProfiles = profiles.filter(
          (profile) => profile.id !== undefined && selectedRows.indexOf(profile.id) === -1
        );
        setProfiles([...updatedProfiles]);
        setSelectedRows([]);
        toast.success("Rows deleted successfully!");
      } catch (error: any) {
        console.error(error);
        toast.error(error.message);
      }
    }
  };

  useEffect(() => {
    tableRefProfiles.current && tableRefProfiles.current.onQueryChange();
  }, [profiles]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search(): void {
    const filteredProfiles = originalProfiles.filter((profile) =>
      profile.name?.toLowerCase().includes(selectedName.toLowerCase())
    );
    setProfiles(filteredProfiles);
  }

  return (
    <div className="wrapper">
      <p style={{
        padding: "1em 1.5em",
        color: "#666",
        lineHeight: "1.5",
        margin: "0 0 1em 0"
      }}>
        {i18n["smsc.smsRoutingTables.smsRoutingTableList.description"] || "A number of files point to the identifiers of Routing Classes e.g  GSM_P2P_mtForwarder.cfg, GSM_P2M_mtForwarder.cfg. Following deployment of the configuration, these files (GSM_P2P_mtForwarder.cfg, GSM_P2M_mtForwarder.cfg etc...) should be updated to the align with the newly generated ISMSC_moRoutingList.cfg  and ISMSC_moRoutingClass.cfg"}
      </p>
      <Card className="content-card">
        <div className="form-row">
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>
              <ValidatorForm onSubmit={() => {}}>
                <SelectValidator
                  label={
                    i18n["smsc.routingClasses.routingClassList.name"] || "Name"
                  }
                  margin="normal"
                  variant="outlined"
                  style={{
                    minWidth: "15em",
                    marginRight: "1rem",
                  }}
                  children={["Any", ...profiles.map((c) => c.name)].map(
                    (name) => (
                      <MenuItem key={name} value={name === "Any" ? "" : name}>
                        {name}
                      </MenuItem>
                    )
                  )}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setSelectedName(e.target.value)}
                  value={selectedName}
                />
              </ValidatorForm>
            </div>
          </div>
        </div>

        <CardContent>
          <MaterialTable
            tableRef={tableRefProfiles}
            icons={tableIcons}
            data={populateProfilesData}
            columns={[
              {
                field: "name",
                title:
                  i18n["smsc.routingClasses.routingClassList.name"] || "Name",
              },
              {
                field: "mtProtocol",
                title:
                  i18n["smsc.routingClasses.routingClassList.mtProtocol"] ||
                  "Protocol",
              },
              {
                field: "routes",
                title:
                  i18n["smsc.routingClasses.routingClassList.routes"] ||
                  "Routes",
                render: (rowData) => {
                  return rowData.routes
                    .map(
                      (route) => `${route.routingInterface} - ${route.priority}`
                    )
                    .join(", ");
                },
              },
            ]}
            options={{
              selection: true,
              actionsColumnIndex: -1,
              toolbar: false,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              emptyRowsWhenPaging: false,
              headerStyle: { fontWeight: "bold" },
            }}
            onRowClick={(event, rowData) => {
              navigate(`/smscRoutingClassForm/${rowData.id}`);
            }}
            onSelectionChange={(rows) => handleRowSelection(rows)}
          />
        </CardContent>
        <CardContent>
          <Button
            variant="contained"
            color="primary"
            className="request-handler-add-button"
            aria-label="Add"
            onClick={() => navigate("/smscRoutingClassForm/new")}
            style={{
              marginLeft: "15px",
              marginTop: "2rem",
              marginBottom: "1rem",
            }}
            disabled={
              !SecurityService.checkPermission(
                "SMSC_ROUTING_CLASS_CREATE_PERMISSION"
              )
            }
          >
            {i18n["button.add"] || "Add"}
          </Button>
          <span style={{ marginLeft: "10px" }}>
            <Button
              variant="contained"
              color="secondary"
              type="button"
              onClick={handleDelete}
              style={{ marginTop: "2rem", marginBottom: "1rem" }}
              className="request-handler-add-button"
              disabled={
                !SecurityService.checkPermission(
                  "SMSC_ROUTING_CLASS_DELETE_PERMISSION"
                )
              }
            >
              {i18n["button.delete"] || "Delete"}
            </Button>
          </span>
        </CardContent>
      </Card>
    </div>
  );
}

export default SmscRoutingClasses;





