import { <PERSON><PERSON>, <PERSON>, <PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, MenuItem } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscIpListsService } from "@pnmui/common/services/smsc/ipListsService";
import { paginate } from "@pnmui/common/utils";
import React, { ChangeEvent, useEffect, useState } from "react";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import { IpList } from "../types";

interface TableData {
  data: IpList[];
  page: number;
  totalCount: number;
}

function SmscIpLists() {
  const [originalIpLists, setOriginalIpLists] = useState<IpList[]>([]);
  const [ipLists, setIpLists] = useState<IpList[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const navigate = useNavigate();
  const tableRef = React.createRef<any>();

  useEffect(() => {
    const subscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });
    populateIpLists();

    return () => subscription.unsubscribe();
  }, []);

  async function populateIpLists(): Promise<void> {
    try {
      const ipListsResponse = await SmscIpListsService.getIpLists();
      const ipListsList: IpList[] = Array.isArray(ipListsResponse)
        ? ipListsResponse
        : [];
      setIpLists(ipListsList);
      setOriginalIpLists(ipListsList);
    } catch (error) {
      console.error("Error fetching IP lists:", error);
    }
  }

  async function populateIpListsData(query: {
    page: number;
    pageSize: number;
  }): Promise<TableData> {
    const paginatedList = paginate(ipLists, query.page, query.pageSize);
    return {
      data: paginatedList,
      page: query.page,
      totalCount: ipLists.length,
    };
  }

  const handleRowSelection = (rows: IpList[]): void => {
    setSelectedRows(rows.map((row) => row.id || ""));
  };

  const handleDelete = async (): Promise<void> => {
    if (selectedRows.length === 0) {
      console.error("No rows selected for deletion.");
      return;
    }
    if (window.confirm("Are you sure you want to delete?")) {
      try {
        await Promise.all(
          selectedRows.map(async (id) => {
            return await SmscIpListsService.deleteIpListById(id);
          })
        );
        const updatedIpLists = ipLists.filter(
          (ipList) =>
            ipList.id !== undefined && selectedRows.indexOf(ipList.id) === -1
        );
        setIpLists([...updatedIpLists]);
        setSelectedRows([]);
        toast.success("IP Lists deleted successfully!");
      } catch (error: any) {
        console.error(error);
        toast.error(error.message);
      }
    }
  };

  useEffect(() => {
    tableRef.current && tableRef.current.onQueryChange();
  }, [ipLists]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search(): void {
    const filteredIpLists = originalIpLists.filter((ipList) =>
      ipList.name?.toLowerCase().includes(selectedName.toLowerCase())
    );
    setIpLists(filteredIpLists);
  }

  return (
    <div className="wrapper">
      <Card className="content-card">
        <div className="form-row">
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>
              <ValidatorForm onSubmit={() => {}}>
                <SelectValidator
                  label={i18n["ims.ipLists.name"] || "Name"}
                  margin="normal"
                  variant="outlined"
                  style={{
                    minWidth: "15em",
                    marginRight: "1rem",
                  }}
                  children={["Any", ...ipLists.map((c) => c.name)].map(
                    (name) => (
                      <MenuItem key={name} value={name === "Any" ? "" : name}>
                        {name}
                      </MenuItem>
                    )
                  )}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setSelectedName(e.target.value)
                  }
                  value={selectedName}
                />
              </ValidatorForm>
            </div>
          </div>
        </div>

        <CardContent>
          <MaterialTable
            tableRef={tableRef}
            icons={tableIcons}
            data={populateIpListsData}
            columns={[
              {
                field: "name",
                title: i18n["ims.ipLists.name"] || "Name",
              },
              {
                field: "ipAddresses",
                title: i18n["ims.ipLists.ipAddresses"] || "IP Addresses",
                render: (rowData) => rowData.ipAddresses?.join(", ") || "",
              },
              {
                field: "lastUpdatedTime",
                title: i18n["ims.ipLists.lastUpdated"] || "Last Updated",
                render: (rowData) =>
                  new Date(rowData.lastUpdatedTime).toLocaleString(),
              },
            ]}
            options={{
              selection: true,
              actionsColumnIndex: -1,
              toolbar: false,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              emptyRowsWhenPaging: false,
              headerStyle: { fontWeight: "bold" },
            }}
            onRowClick={(event, rowData) => {
              navigate(`/smscIpListsForm/${rowData.id}`);
            }}
            onSelectionChange={(rows) => handleRowSelection(rows)}
          />
        </CardContent>
        <Button
          variant="contained"
          color="primary"
          className="request-handler-add-button"
          aria-label="Add"
          onClick={() => navigate("/smscIpListsForm/new")}
          style={{
            marginLeft: "15px",
            marginTop: "2rem",
            marginBottom: "1rem",
          }}
          disabled={
            !SecurityService.checkPermission("SMSC_IP_LISTS_CREATE_PERMISSION")
          }
        >
          {i18n["button.add"] || "Add"}
        </Button>

        <span style={{ marginLeft: "10px" }}>
          <Button
            variant="contained"
            color="secondary"
            type="button"
            onClick={handleDelete}
            style={{ marginTop: "2rem", marginBottom: "1rem" }}
            disabled={
              !SecurityService.checkPermission(
                "SMSC_IP_LISTS_DELETE_PERMISSION"
              )
            }
          >
            {i18n["button.delete"] || "Delete"}
          </Button>
        </span>
      </Card>
    </div>
  );
}

export default SmscIpLists;
