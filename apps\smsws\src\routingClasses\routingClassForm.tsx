import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  FormControlLabel,
  MenuItem,
  Paper,
  Switch,
  TextField,
} from "@mui/material";
import { SecurityService } from "@pnmui/common/services/securityService";
import { ChangeEvent, useEffect, useState } from "react";
import {
  SelectValidator,
  TextValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  CommandCell,
  ErrorsDisplay,
  InputCell,
  SelectCell,
} from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SmscDeliveryAndRetryProfilesService } from "@pnmui/common/services/smsc/deliveryAndRetryProfilesService";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import { SmscResourcePoliciesService } from "@pnmui/common/services/smsc/resourcePoliciesService";
import { SmscRoutingClassesService } from "@pnmui/common/services/smsc/routingClassesService";
import { process } from "@progress/kendo-data-query";
import { GridItemChangeEvent, GridToolbar } from "@progress/kendo-react-grid";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import "../common.css";
import { Column, Grid } from "../components/KendoGridWrapper";
import {
  DeliveryAndRetryProfile,
  EnumerationOption,
  ResourcePolicy,
} from "../types";

interface Route {
  id?: string | number;
  routeId?: string;
  routeName?: string;
  deliveryAndRetryProfile?: DeliveryAndRetryProfile | null;
  resourcePolicy?: ResourcePolicy | null;
  inEdit?: boolean;
}

interface RoutingClassFormModel {
  id?: string | number | null;
  name: string;
  mtProtocol: string;
  routes: Route[];
  p2pMessageStore: boolean;
  interworkingStoragePolicy: string;
  consumerPolicy: string;
  retryProfile: string;
  auxiliaryServiceEnabled: boolean;
  createdTime?: string;
  lastUpdatedTime?: string;
}

interface RouteErrors {
  routeId?: string;
  routeName?: string;
  deliveryAndRetryProfile?: string;
  resourcePolicy?: string;
}

interface FormErrors {
  routes: Record<string | number, RouteErrors>;
}

interface Enumerations {
  [key: string]: EnumerationOption[];
}

const SmscRoutingClassForm = () => {
  const navigate = useNavigate();
  const [retryProfileInputValue, setRetryProfileInputValue] = useState<string>("");

  const params = useParams<{ id: string }>();
  const [errors, setErrors] = useState<FormErrors>({
    routes: {}
  });

  const [enumerations, setEnumerations] = useState<Enumerations>({});
  const [resourcePolicies, setResourcePolicies] = useState<ResourcePolicy[]>([]);
  const [retryProfiles, setRetryProfiles] = useState<DeliveryAndRetryProfile[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [routingClass, setRoutingClass] = useState<RoutingClassFormModel>({
    name: "",
    mtProtocol: "",
    routes: [],
    p2pMessageStore: true,
    interworkingStoragePolicy: "",
    consumerPolicy: "",
    retryProfile: "",
    auxiliaryServiceEnabled: true,
  });

  const [errorResponse, setErrorResponse] = useState<any>(null);

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });
    SmscEnumerationsService.getEnumerations().then(setEnumerations);
    SmscResourcePoliciesService.getResourcePolicies().then(setResourcePolicies);
    SmscDeliveryAndRetryProfilesService.getDeliveryAndRetryProfiles().then(
      setRetryProfiles
    );
    if (params.id !== "new") {
      SmscRoutingClassesService.getRoutingClassById(
        params.id === "new" ? null : params.id
      ).then((data: any) => {
        // Map deliveryAndRetryProfile from BE to retryProfile in UI state
        const updatedData = {
          ...data,
          retryProfile: data.deliveryAndRetryProfile || "",
        };
        setRoutingClass(updatedData);
      });
    }

    return () => subscription.unsubscribe();
  }, [params.id]);

  const handleChange = (e: ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>): void => {
    if (!e.target.name) return;

    if (e.target.name.indexOf(".") > -1) {
      const [parent, child] = e.target.name.split(".");
      setRoutingClass({
        ...routingClass,
        [parent]: { ...routingClass[parent as keyof RoutingClassFormModel], [child]: e.target.value },
      });
    } else {
      setRoutingClass({ ...routingClass, [e.target.name]: e.target.value });
    }

    setErrors((prevErrors) => ({ ...prevErrors, [e.target.name]: "" }));
  };

  const handleSubmit = (): void => {
    const cleanedRoutes = routingClass.routes.map((route) => {
      const {
        id,
        routeOrder,
        routingInterface,
        responseTimeout,
        priority,
        auxServiceEnabled,
        resourcePolicy,
      } = route;

      return {
        ...(id && typeof id === "number" && id < 10000 ? { id } : {}),
        routeOrder,
        routingInterface: routingInterface.value || routingInterface,
        responseTimeout,
        priority,
        auxServiceEnabled: auxServiceEnabled ?? false,
        resourcePolicy: resourcePolicy?.id || resourcePolicy,
      };
    });

    const payload = {
      id: routingClass.id || undefined,
      name: routingClass.name,
      mtProtocol: routingClass.mtProtocol,
      routes: cleanedRoutes,
      interworkingStoragePolicy: routingClass.interworkingStoragePolicy || null,
      consumerPolicy: routingClass.consumerPolicy || null,
      deliveryAndRetryProfile: routingClass.retryProfile || null,
      auxiliaryService: routingClass.auxiliaryService || null,
    };

    SmscRoutingClassesService.saveRoutingClass(payload)
      .then((data) => {
        toast.success("RoutingClass saved successfully");
        navigate("/smscRoutingClasses");
      })
      .catch((error: any) => {
        console.error("handleSubmit error", error);
        setErrorResponse(error);

        if (
          error.response &&
          error.response.data &&
          error.response.data.errors
        ) {
          const backendErrors = error.response.data.errors;
          setErrors(backendErrors);
        } else {
          toast.error("Failed to save routingClass: " + error.message);
        }
      });
  };

  function goBack(): void {
    navigate(-1);
  }

  const addNew = (): void => {
    const newDataItem = {
      inEdit: true,
      id: Date.now(),
    };
    setRoutingClass({
      ...routingClass,
      routes: [newDataItem, ...routingClass.routes],
    });
  };

  function onItemChange(event: GridItemChangeEvent): void {
    const updatedRoutes = routingClass.routes.map((item) =>
      item.id === event.dataItem.id
        ? { ...item, [event.field]: event.value, inEdit: true }
        : item
    );

    setRoutingClass({
      ...routingClass,
      routes: updatedRoutes,
    });

    if (event.field === "auxServiceEnabled" && event.value) {
      setRoutingClass((prev) => ({
        ...prev,
        auxiliaryService: prev.auxiliaryService || {
          localForwarding: false,
          responseTimeout: "",
          brokerResourcePolicy: "",
        },
      }));
    }
  }

  const addNewRetryProfile = (newRetryProfileName) => {
    if (!newRetryProfileName) return;
    SmscDeliveryAndRetryProfilesService.saveRetryProfileWithName(
      newRetryProfileName
    )
      .then((data) => {
        setRetryProfiles((prevProfiles) => [...prevProfiles, data]);
        setRoutingClass({ ...routingClass, retryProfile: data.id });
        setRetryProfileInputValue("");
        toast.success("Retry Profile created successfully");
      })
      .catch((error) => {
        console.error("Failed to create new retry profile:", error);
        toast.error("Failed to create new retry profile.");
      });
  };

  const enterEdit = (dataItem) => {
    const updatedRoutes = routingClass.routes.map((item) =>
      item.id === dataItem.id ? { ...item, inEdit: true } : item
    );
    setRoutingClass({ ...routingClass, routes: updatedRoutes });
  };

  const dataState = {
    sort: [{ field: "order", dir: "asc" }],
  };

  const routesGridData = process(
    routingClass.routes
      ? routingClass.routes.map((r) => ({
          ...r,
          resourcePolicy: r.resourcePolicy?.id
            ? r.resourcePolicy
            : resourcePolicies.find((p) => p.id === r.resourcePolicy),
        }))
      : [],
    dataState
  );

  const validateRoute = (route) => {
    const errors = {};

    if (!route.routingInterface) {
      errors.routingInterface = "Interface type is required";
    }

    if (!route.resourcePolicy) {
      errors.resourcePolicy = "Resource policy is required";
    }

    if (!route.responseTimeout || route.responseTimeout < 0) {
      errors.responseTimeout = "Response timeout must be a positive number";
    }

    if (!route.routeOrder || route.routeOrder < 0) {
      errors.routeOrder = "Route Order must be a positive number";
    }

    if (!route.priority) {
      errors.priority = "Priority is required";
    }

    return errors;
  };

  const getErrorMessage = (id: string | number | undefined, field: string): string | undefined => {
    return errors?.routes?.[id]?.[field];
  };

  return (
    <div style={{ padding: "0.5em", paddingTop: "2em" }}>
      <ValidatorForm onSubmit={handleSubmit} className="tango-form">
        <div style={{ marginLeft: "1em" }}>
          <Card>
            <CardContent>
              <ErrorsDisplay
                errorResponse={errorResponse}
                keyPrefix="smsws.routingClass.form.keys"
              />
              <div style={{ display: "flex" }}>
                <TextValidator
                  label="Name"
                  onChange={handleChange}
                  name="name"
                  value={routingClass.name}
                  validators={["required"]}
                  errorMessages={["Name is required"]}
                />

                <SelectValidator
                  label="MT Protocol"
                  onChange={handleChange}
                  name="mtProtocol"
                  value={routingClass.mtProtocol}
                  validators={["required"]}
                  errorMessages={["MT Protocol is required"]}
                >
                  {enumerations?.mTProtocol?.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.displayText}
                    </MenuItem>
                  ))}
                </SelectValidator>
              </div>
              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <span className={"accordion-title"}>Route(s)</span>
                </AccordionSummary>
                <AccordionDetails className="flex-container">
                  <div
                    style={{
                      width: "100%",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                    }}
                  >
                    <Grid
                      editField="inEdit"
                      onItemChange={onItemChange}
                      editable={true}
                      style={{ width: "100%" }}
                      data={routesGridData}
                      total={routingClass.routes?.length || 0}
                      sort={[{ field: "routeOrder", dir: "desc" }]}
                      sortable={true}
                    >
                      <GridToolbar>
                        <button
                          type={"button"}
                          className="k-primary k-button k-grid-edit-command"
                          style={{ position: "absolute", right: "1em" }}
                          onClick={addNew}
                        >
                          Add
                        </button>
                      </GridToolbar>

                      <Column
                        field={"routeOrder"}
                        title={"Order"}
                        cell={(props) => (
                          <InputCell
                            {...props}
                            sx={{ width: "3em !important", maxWidth: "3em !important", minWidth: "3em !important" }}
                            style={{ width: "3em !important", maxWidth: "3em !important", minWidth: "3em !important" }}
                            type="number"
                            error={getErrorMessage(props.dataItem.id, 'routeOrder')}
                          />
                        )}
                      />
                      <Column
                        field="routingInterface"
                        title="Interface Type"
                        editable={true}
                        cell={(props) => (
                          <SelectCell
                            {...props}
                            options={enumerations.routingInterfaceType}
                            error={getErrorMessage(props.dataItem.id, 'routingInterface')}
                          />
                        )}
                      />
                      <Column
                        field="resourcePolicy"
                        title="Resource Policy"
                        editable={true}
                        cell={(props) => (
                          <SelectCell
                            {...props}
                            options={resourcePolicies}
                            error={getErrorMessage(props.dataItem.id, 'resourcePolicy')}
                          />
                        )}
                      />
                      <Column
                        field={"responseTimeout"}
                        title={"Response Timeout in Seconds"}
                        cell={(props) => (
                          <InputCell
                            {...props}
                            type="number"
                            error={getErrorMessage(props.dataItem.id, 'responseTimeout')}
                          />
                        )}
                      />
                      <Column
                        field="priority"
                        title="Priority"
                        editable={true}
                        cell={(props) => (
                          <SelectCell
                            {...props}
                            options={enumerations?.mORoutePriority?.map(
                              (p) => p.value
                            )}
                            error={getErrorMessage(props.dataItem.id, 'priority')}
                          />
                        )}
                      />
                      <Column
                        field={"auxServiceEnabled"}
                        title={"Auxiliary Service"}
                        cell={(props) => (
                          <td>
                            <Switch
                              checked={props.dataItem.auxServiceEnabled || false}
                              onChange={(e) =>
                                onItemChange({
                                  dataItem: props.dataItem,
                                  field: "auxServiceEnabled",
                                  value: e.target.checked,
                                })
                              }
                              color="primary"
                            />
                          </td>
                        )}
                      />

                      <Column
                        cell={(props) => (
                          <CommandCell
                            {...props}
                            item={routingClass}
                            onChange={(data) => {
                              setRoutingClass({
                                ...routingClass,
                                routes: data.map((r) => ({
                                  ...r,
                                  routingInterface: r.routingInterface.value || r.routingInterface,
                                  resourcePolicy: r.resourcePolicy?.id || r.resourcePolicy,
                                })),
                              });
                            }}
                            edit={(dataItem) => enterEdit(dataItem)}
                            onSave={(dataItem) => {
                              const errors = validateRoute(dataItem);
                              if (Object.keys(errors).length > 0) {
                                setErrors(prev => ({
                                  ...prev,
                                  routes: {
                                    ...prev.routes,
                                    [dataItem.id]: errors
                                  }
                                }));
                                toast.error(Object.values(errors)[0]);
                                return false;
                              }
                              setErrors(prev => {
                                const newErrors = { ...prev };
                                if (newErrors.routes) {
                                  delete newErrors.routes[dataItem.id];
                                }
                                return newErrors;
                              });
                              return true;
                            }}
                            gridProp={"routes"}
                          />
                        )}
                        filterable={false}
                      />
                    </Grid>
                  </div>
                </AccordionDetails>
              </Accordion>
              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                expanded={
                  routingClass.interworkingStoragePolicy ||
                  routingClass.consumerPolicy ||
                  routingClass.retryProfile
                }
              >
                <AccordionSummary
                  expandIcon={
                    <ToggleOffIcon
                      style={{
                        color:
                          routingClass.interworkingStoragePolicy ||
                          routingClass.consumerPolicy ||
                          routingClass.retryProfile
                            ? "#3f51b5"
                            : "gray",
                        fontSize: "3em",
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setRoutingClass((prev) => ({
                          ...prev,
                          interworkingStoragePolicy:
                            prev.interworkingStoragePolicy ? "" : 1,
                          consumerPolicy: prev.consumerPolicy ? "" : 1,
                          retryProfile: prev.retryProfile
                            ? null
                            : prev.retryProfile,
                        }));
                      }}
                    />
                  }
                >
                  <span className={"accordion-title"}>P2P Message Store</span>
                </AccordionSummary>
                <AccordionDetails className="flex-container">
                  <div
                    style={{
                      width: "100%",
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "flex-start",
                    }}
                  >
                    <SelectValidator
                      label="Interworking Storage Policy"
                      onChange={handleChange}
                      name="interworkingStoragePolicy"
                      value={routingClass.interworkingStoragePolicy || ""}
                    >
                      {resourcePolicies.map((item, index) => (
                        <MenuItem key={item.id} value={item.id}>
                          {item.name}
                        </MenuItem>
                      ))}
                    </SelectValidator>

                    <SelectValidator
                      label="Consumer Policy"
                      onChange={handleChange}
                      name="consumerPolicy"
                      value={routingClass.consumerPolicy || ""}
                    >
                      {resourcePolicies.map((item, index) => (
                        <MenuItem key={item.id} value={item.id}>
                          {item.name}
                        </MenuItem>
                      ))}
                    </SelectValidator>

                    <Autocomplete
                      label="Retry Profile"
                      options={retryProfiles}
                      getOptionLabel={(option) => {
                        if (typeof option === "string") return option;
                        return typeof option.name === "object"
                          ? option.name.name
                          : option.name;
                      }}
                      value={
                        retryProfiles.find(
                          (profile) => profile.id === routingClass.retryProfile
                        ) || null
                      }
                      inputValue={retryProfileInputValue}
                      onInputChange={(event, newInputValue) =>
                        setRetryProfileInputValue(newInputValue)
                      }
                      onChange={(e, value) => {
                        setRoutingClass({
                          ...routingClass,
                          retryProfile: value ? value.id : "",
                        });
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Retry Profile"
                          placeholder="Type to select or add a Retry Profile"
                          fullWidth
                          InputProps={{
                            ...params.InputProps,
                          }}
                        />
                      )}
                      PaperComponent={(props) => (
                        <Paper
                          {...props}
                          sx={{ borderRadius: 2, boxShadow: 1 }}
                        >
                          {props.children}
                          <Button
                            disabled={
                              !retryProfileInputValue ||
                              retryProfiles.some(
                                (profile) =>
                                  profile.name === retryProfileInputValue
                              )
                            }
                            color="primary"
                            fullWidth
                            sx={{ justifyContent: "flex-start", pl: 2 }}
                            onMouseDown={() =>
                              addNewRetryProfile(retryProfileInputValue)
                            }
                          >
                            + Add New
                          </Button>
                        </Paper>
                      )}
                    />
                  </div>
                </AccordionDetails>
              </Accordion>

              {routingClass.routes.some((r) => r.auxServiceEnabled) && (
                <Accordion
                  style={{ marginTop: "1em", borderTop: "none" }}
                  expanded={true}
                >
                  <AccordionSummary>
                    <span className={"accordion-title"}>
                      {i18n["route.auxiliaryService" || "Auxiliary Service"]}
                    </span>
                  </AccordionSummary>
                  <AccordionDetails className="flex-container">
                    <div
                      style={{
                        width: "100%",
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "flex-start",
                      }}
                    >
                      <TextValidator
                        label="Response Timeout in Seconds"
                        type="number"
                        onChange={handleChange}
                        name="auxiliaryService.responseTimeout"
                        value={routingClass.auxiliaryService?.responseTimeout}
                        validators={["required", "minNumber:1"]}
                        errorMessages={[
                          "Response timeout is required",
                          "Response timeout must be at least 1 second",
                        ]}
                      />
                      <FormControlLabel
                        style={{ width: "25em" }}
                        control={
                          <Switch
                            checked={
                              routingClass.auxiliaryService?.localForwarding ||
                              false
                            }
                            onChange={(e) =>
                              setRoutingClass({
                                ...routingClass,
                                auxiliaryService: {
                                  ...routingClass.auxiliaryService,
                                  localForwarding: e.target.checked,
                                },
                              })
                            }
                            value={
                              routingClass.auxiliaryService?.localForwarding ||
                              false
                            }
                          />
                        }
                        label="LocalForwarding"
                      />
                    </div>
                    <div>
                      <SelectValidator
                        label="Broker Resource Policy"
                        onChange={handleChange}
                        name="auxiliaryService.brokerResourcePolicy"
                        validators={["required"]}
                        errorMessages={["Broker Resource Policy is required"]}
                        value={
                          routingClass.auxiliaryService?.brokerResourcePolicy
                            ?.id ||
                          routingClass.auxiliaryService?.brokerResourcePolicy ||
                          ""
                        }
                      >
                        {resourcePolicies.map((item, index) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.name}
                          </MenuItem>
                        ))}
                      </SelectValidator>
                    </div>
                  </AccordionDetails>
                </Accordion>
              )}
            </CardContent>
            <CardActions className="card-actions content-card-actions">
              <Button
                variant="contained"
                color="secondary"
                type="button"
                onClick={goBack.bind(this)}
              >
                {i18n["button.cancel"] || "Cancel"}
              </Button>

              {SecurityService.checkPermission(
                "SMSC_ROUTING_CLASS_UPDATE_PERMISSION"
              ) && (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  className="request-handler-submit-button"
                >
                  {i18n["button.submit"] || "Submit"}
                </Button>
              )}
            </CardActions>
          </Card>
        </div>
      </ValidatorForm>
    </div>
  );
};

export default SmscRoutingClassForm;





