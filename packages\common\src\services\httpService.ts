import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import ErrorService from "./errorService";
import { HttpRequestConfig, HttpResponse } from "./types";

const axiosInstance: AxiosInstance = axios.create();
axiosInstance.defaults.timeout = 30000;

// Add request interceptor for request management
axiosInstance.interceptors.request.use(
  (config) => {
    // Pass through all valid requests
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    if (response.status > 399) {
      ErrorService.processServerCallError(response);
    }
    return response;
  },
  (error: AxiosError): Promise<any> => {
    // Check if this is a language service or other essential service
    const url = error.config?.url || '';
    const isEssentialService = [
      '/i18n',
      '/language',
      '/properties',
      '/permissions'
    ].some(path => url.includes(path));

    // If it's an essential service, let the error propagate normally
    if (isEssentialService) {
      if (error.response && error.response.status === 440) {
        window.location.href = `${window.location.href
          .substring(0, window.location.href.indexOf("react") - 1)
          .replace(window.location.origin, "")}/login.jsp`;
      }
      return ErrorService.processServerCallError(error);
    }

    // For all other errors, process normally
    if (error.response && error.response.status === 440) {
      window.location.href = `${window.location.href
        .substring(0, window.location.href.indexOf("react") - 1)
        .replace(window.location.origin, "")}/login.jsp`;
    }
    return ErrorService.processServerCallError(error);
  }
);

let routingUrl: string;
let multiPartRoutingUrl: string;

interface Properties {
  subscribe: (observer: (data: Record<string, string>) => void) => { unsubscribe: () => void };
}

export const HttpService = {
  // Modified init to accept properties as a parameter
  init(properties?: Properties): void {
    try {
      const baseUrl = window.location.href
        .substring(0, window.location.href.indexOf("react") - 1)
        .replace(window.location.origin, "")
        ? `${window.location.href
            .substring(0, window.location.href.indexOf("react") - 1)
            .replace(window.location.origin, "")}`
        : "/pmi";
      routingUrl = `${baseUrl}/routeRequest`;
      multiPartRoutingUrl = `${baseUrl}/routeMultipartRequest`;

      // If properties is provided, set up a subscription
      if (properties) {
        properties.subscribe(() => {
          // This will update the URLs if properties change
          routingUrl = `${baseUrl}/routeRequest`;
          multiPartRoutingUrl = `${baseUrl}/routeMultipartRequest`;
        });
      }
    } catch (e) {
      console.error("Error initializing HttpService:", e);
    }
  },

  getWithoutRouting(url: string): Promise<AxiosResponse> {
    return axiosInstance.get(url);
  },

  get<T = any>(url: string, options?: HttpRequestConfig): Promise<HttpResponse<T>> {
    return this.performRequest("get", url, options, null) as Promise<HttpResponse<T>>;
  },

  post<T = any>(url: string, options?: HttpRequestConfig, body?: any): Promise<HttpResponse<T>> {
    return this.performRequest("post", url, options, body) as Promise<HttpResponse<T>>;
  },

  upload<T = any>(url: string, options?: HttpRequestConfig, file?: File): Promise<HttpResponse<T>> {
    return this.performRequest("upload", url, options, file) as Promise<HttpResponse<T>>;
  },

  put<T = any>(url: string, options?: HttpRequestConfig, body?: any): Promise<HttpResponse<T>> {
    return this.performRequest("put", url, options, body) as Promise<HttpResponse<T>>;
  },

  delete<T = any>(url: string, options?: HttpRequestConfig): Promise<HttpResponse<T>> {
    return this.performRequest("delete", url, options, null) as Promise<HttpResponse<T>>;
  },

  performRequest<T = any>(
    method: string,
    url: string,
    options?: HttpRequestConfig,
    body?: any
  ): Promise<HttpResponse<T>> {
    try {
      const opts: HttpRequestConfig = options || {};
      opts.headers = opts.headers || {};
      // Check if the URL is valid before making the request
      if (!url || url.trim() === '') {
        console.warn('Skipping request with empty URL');
        return Promise.reject(new Error('Empty URL'));
      }

      // For routeRequest calls, set the target URL header
      if (process.env.STANDALONE === 'false' && url.startsWith('http') && !routingUrl.includes(url)) {
        opts.headers.TargetURL = url;
      }

      if (method === "upload") {
        const FormData = require("form-data");
        let data = new FormData();
        data.append("files[]", body);

        let config: HttpRequestConfig = {
          maxBodyLength: Infinity,
          headers: {
            Accept: "*/*",
            "Content-Type": "multipart/form-data",
            ...opts.headers,
          },
          data: data,
        };

        return axiosInstance.post(process.env.STANDALONE === 'false' ? multiPartRoutingUrl : url, data, config as AxiosRequestConfig);
      }

      opts.headers.Method = method.toUpperCase();

      // Handle different HTTP methods appropriately
      if (method === "get" || method === "delete") {
        return axiosInstance.get(process.env.STANDALONE === 'false' ? routingUrl : url, opts as AxiosRequestConfig);
      } else if (method === "put") {
        return axiosInstance.put(process.env.STANDALONE === 'false' ? routingUrl : url, body, opts as AxiosRequestConfig);
      } else {
        return axiosInstance.post(process.env.STANDALONE === 'false' ? routingUrl : url, body, opts as AxiosRequestConfig);
      }
    } catch (e) {
      console.error("Error performing request:", e);
      return Promise.reject(e);
    }
  },
};

// Initialize with basic settings, will be updated later with properties
HttpService.init();
