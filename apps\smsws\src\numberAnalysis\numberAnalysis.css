/* General text styling */
.description-text {
  margin-bottom: 1.5em;
  line-height: 1.5;
}

.description-text p {
  margin: 0;
  color: #333;
}

.section-header {
  margin-bottom: 1.5em;
}

.section-header h3 {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 500;
  color: #333;
}

.section-header p {
  margin: 0;
  line-height: 1.5;
  color: #333;
}

.section-description {
  margin-bottom: 1em;
  width: 100%;
}

.section-description p {
  margin: 0;
  line-height: 1.5;
  color: #333;
}

/* Reload charging methods section */
.reload-charging-methods {
  display: flex;
  align-items: center; /* Center alignment for better vertical alignment */
  margin-bottom: 1.5em;
  gap: 16px; /* Increased gap for better spacing */
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.reload-charging-methods .MuiFormControl-root {
  flex: 0 0 250px; /* Fixed width instead of flex-grow */
  max-width: 250px;
  margin-bottom: 0;
}

.reload-charging-methods button {
  height: 40px; /* Increased height to match input field better */
  margin: 0; /* Remove all margins */
  white-space: nowrap;
  padding: 0 16px; /* Add horizontal padding for better appearance */
}

.reload-charging-methods .optional-label {
  margin-top: 5px;
  align-self: flex-start;
}

/* Optional label */
.optional-label {
  color: #666;
  font-style: italic;
  font-size: 0.8rem;
  margin-left: 0.5em;
}

/* Grid styling */
.k-grid {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 1em;
  background-color: white; /* Ensure white background */
}

.k-grid-header {
  background-color: white !important;
}

.k-grid-header th {
  font-weight: bold;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  background-color: white !important;
}

.k-grid-content td {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  background-color: white !important;
}

/* Remove alternating row colors */
.k-grid tr {
  background-color: white !important;
}

.k-grid tr:nth-child(even) {
  background-color: white !important;
}

.k-grid tr:hover {
  background-color: white !important;
}

/* Section styling */
.section-title {
  font-weight: bold;
  font-size: 1rem;
  color: #333;
  margin-bottom: 1em;
  margin-top: 1.5em;
}

/* Remove accordion styling */
.MuiAccordion-root {
  box-shadow: none !important;
  border: none !important;
  margin-bottom: 1em !important;
}

.MuiAccordionSummary-root {
  background-color: white !important;
  padding: 0 !important;
}

.MuiAccordionDetails-root {
  padding: 16px 0 !important;
}

/* Boxed accordion styling */
.MuiAccordion-root.boxed-accordion {
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  background-color: #fff !important;
  margin-top: 1.5em !important;
  margin-bottom: 1.5em !important;
}

.MuiAccordion-root.boxed-accordion:before {
  display: none !important;
}

.MuiAccordion-root.boxed-accordion.Mui-expanded {
  margin: 1.5em 0 !important;
}

.MuiAccordion-root.boxed-accordion .MuiAccordionSummary-root {
  padding: 16px !important;
  background-color: white !important;
}

.MuiAccordion-root.boxed-accordion .MuiAccordionDetails-root {
  padding: 16px !important;
}

/* Form fields */
.MuiFormControl-root {
  margin-bottom: 1em;
  width: 100%;
}

/* Select fields */
.MuiSelect-select {
  min-width: 200px;
}

/* Fixed width for select validators */
.section-container .MuiFormControl-root {
  max-width: 300px;
  width: 100%;
}

/* B-Number section specific styling */
.b-number-selects {
  display: flex;
  flex-direction: row;
  gap: 20px;
  flex-wrap: wrap;
}

.b-number-selects .MuiFormControl-root {
  flex: 0 0 300px;
  max-width: 300px;
}

/* Buttons */
.card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

.card-actions button {
  margin-left: 8px;
}

/* Grid cell styling */
.k-grid td .MuiFormControl-root {
  margin-bottom: 0;
}

/* Toggle switch styling */
.MuiSwitch-root {
  margin: 0 !important;
}

/* Command cell buttons */
.k-grid td button {
  margin: 0 4px;
  padding: 4px 8px;
  min-width: 30px;
}

/* Grid cell alignment */
.k-grid td {
  vertical-align: middle;
  text-align: left;
}

/* Grid command cell styling */
.k-command-cell {
  text-align: center !important;
}

/* Grid button styling */
.k-grid-edit-command, .k-grid-save-command {
  color: #1976d2;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  padding: 4px 8px;
  margin: 0 2px;
}

.k-grid-delete-command, .k-grid-cancel-command {
  color: #f44336;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  padding: 4px 8px;
  margin: 0 2px;
}

/* Add button in grid toolbar */
.k-grid-toolbar button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
  padding: 6px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.k-grid-toolbar button:hover {
  background-color: #e0e0e0;
}

/* Card styling - removed to use global card styles from App.css */

/* Force recalculate class for grid layout fix */
.k-grid.force-recalculate {
  display: block !important;
}

.k-grid.force-recalculate .k-grid-header,
.k-grid.force-recalculate .k-grid-content {
  display: table !important;
  width: 100% !important;
  table-layout: fixed !important;
}

/* Ensure consistent cell sizing and alignment */
.k-grid td {
  box-sizing: border-box !important;
  height: 48px !important; /* Fixed height for all cells */
  vertical-align: middle !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Ensure grid columns have consistent widths */
.k-grid-header th {
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* Fix for grid layout issues */
.k-grid-table {
  table-layout: fixed !important;
  width: 100% !important;
}

/* Ensure grid content has proper layout */
.k-grid-content {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* Fix for edit mode cells */
.k-grid td .MuiFormControl-root,
.k-grid td .MuiInputBase-root {
  width: 100% !important;
  min-height: 32px !important;
}

/* Fix for grid alignment issues when adding new entries */
.k-grid-add-row td {
  height: 48px !important;
  vertical-align: middle !important;
  box-sizing: border-box !important;
}

/* Ensure consistent column widths */
.k-grid-table {
  width: 100% !important;
  table-layout: fixed !important;
}

/* Fix for grid layout when adding new rows */
.k-grid-edit-row > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding: 8px !important;
}

/* Ensure grid cells maintain proper alignment */
.k-grid-content tr {
  display: table-row !important;
}

.k-grid-content td {
  display: table-cell !important;
}

/* Fix for grid header alignment */
.k-grid-header-wrap {
  border-right: 0 !important;
}

/* Fix for grid content alignment */
.k-grid-content {
  overflow-x: hidden !important;
  overflow-y: auto !important;
}
