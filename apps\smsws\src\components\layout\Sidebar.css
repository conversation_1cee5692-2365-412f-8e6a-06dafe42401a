.sidebar {
  width: 250px;
  height: 100%;
  background-color: #222;
  color: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.logo-container {
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-bottom: 1px solid #333;
}

.logo {
  color: #ff0000;
  font-size: 24px;
  font-weight: bold;
  font-family: Arial, sans-serif;
}

.menu {
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* Main menu items */
.menu-item {
  display: flex;
  flex-direction: column;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: #ccc;
  text-decoration: none;
  border-left: 3px solid transparent;
  transition: background-color 0.2s;
  cursor: pointer;
}

.menu-link:hover, .menu-link.active {
  background-color: #333;
  color: #fff;
  border-left-color: #ff0000;
}

.menu-icon {
  margin-right: 10px;
  display: flex;
  align-items: center;
}

.menu-label {
  flex: 1;
}

.menu-expand {
  display: flex;
  align-items: center;
}

/* Submenu */
.submenu {
  background-color: #1a1a1a;
  overflow: hidden;
  display: block !important; /* Always show submenu */
}

.submenu-item {
  display: flex;
  flex-direction: column;
}

.submenu-link {
  display: flex;
  align-items: center;
  padding: 10px 15px 10px 40px;
  color: #bbb;
  text-decoration: none;
  transition: background-color 0.2s;
  cursor: pointer;
}

.submenu-link:hover, .submenu-link.active {
  background-color: #2a2a2a;
  color: #fff;
}

.submenu-label {
  flex: 1;
}

.submenu-expand {
  display: flex;
  align-items: center;
}

/* Nested submenu */
.nested-submenu {
  background-color: #151515;
  display: block !important; /* Always show nested submenu */
}

.nested-submenu-link {
  display: flex;
  align-items: center;
  padding: 8px 15px 8px 55px;
  color: #aaa;
  text-decoration: none;
  transition: background-color 0.2s;
  cursor: pointer;
}

.nested-submenu-link:hover, .nested-submenu-link.active {
  background-color: #252525;
  color: #fff;
}

.nested-submenu-label {
  flex: 1;
}
