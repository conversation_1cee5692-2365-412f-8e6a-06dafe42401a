import {
  Checkbox,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableSortLabel,
  Toolbar,
  <PERSON><PERSON>ip,
  Typography,
} from "@mui/material";
import _ from "lodash";
import React, { FC, ReactNode, useEffect, useState } from "react";
import classNames from "classnames";
import DeleteIcon from "@mui/icons-material/Delete";
import { ASC, DESC, SortDirection } from "./constants";
import "./table.css";
import { useNavigate } from "react-router-dom";

interface TableHeader {
  id?: string;
  path?: string;
  label: string;
  content?: (item: any, data?: any[]) => ReactNode;
}

interface TableItem {
  id?: string | number;
  msisdn?: string;
  externalId?: string;
  [key: string]: any;
}

interface EnhancedTableToolbarProps {
  numSelected: number;
  raiseDelete?: () => void;
}

interface SimpleTableProps {
  headers?: TableHeader[];
  data?: TableItem[];
  hasOrdering?: boolean;
  handleRowClick?: (item: TableItem) => void;
  onDelete?: (selected: (string | number)[]) => void;
  paginationOptions?: {
    pageSizeOptions?: (number | { label: string; value: number })[];
    totalCount: number;
    pageSize: number;
    onPageChange: (page: number, pageSize: number) => void;
    onRowsPerPageChange: (pageSize: number, page: number) => void;
  };
}

const EnhancedTableToolbar: FC<EnhancedTableToolbarProps> = (props) => {
  const { numSelected, raiseDelete } = props;

  if (numSelected > 0) {
    return (
      <Toolbar className={classNames("root", { highlight: numSelected > 0 })}>
        <div className="title">
          <Typography color="inherit" variant="subtitle1">
            {numSelected} selected
          </Typography>
        </div>
        <div className="spacer" />
        <div className="actions">
          <Tooltip title="Delete">
            <IconButton aria-label="Delete" onClick={raiseDelete}>
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </div>
      </Toolbar>
    );
  }
  return null;
};

const SimpleTable: FC<SimpleTableProps> = (props) => {
  const [headers, setHeaders] = useState<TableHeader[]>(props.headers || []);
  const [selected, setSelected] = useState<(string | number)[]>([]);
  const [order, setOrder] = useState<SortDirection>(ASC);
  const [orderBy, setOrderBy] = useState<string>("");
  const [data, setData] = useState<TableItem[]>(props.data || []);
  // const [page, setPage] = useState(0);
  // const [paginationOptions, setPaginationOptions] = useState(props.paginationOptions || null);

  const navigate = useNavigate();

  useEffect(() => {
    setData(props.data || []);
    setHeaders(props.headers || []);
  }, [props.data, props.headers]);

  function renderCell(item: TableItem, column: TableHeader): ReactNode {
    let cellContent = _.get(item, column.path || column.id || "");
    if (cellContent === true) {
      cellContent = <span className="fa fa-check" />;
    } else if (cellContent === false || typeof cellContent === "undefined") {
      cellContent = "";
    } else cellContent += "";
    return cellContent;
  }

  function handleRowClick(item: TableItem): void {
    const { handleRowClick } = props;
    if (handleRowClick) {
      handleRowClick(item);
    } else {
      headers.forEach((header) => {
        if (header.content) {
          const content = header.content(item);
          if (React.isValidElement(content) && content.props.to) {
            navigate(content.props.to);
          }
        }
      });
    }
  }

  function createKey(item: TableItem, column: TableHeader | string): string {
    const key =
      (item.id || item.msisdn || item.externalId || "") +
      (typeof column === 'string' ? column : (column.path || Math.random().toString()));
    return key;
  }

  function handleClick(_event: React.MouseEvent<HTMLButtonElement>, id: string | number): void {
    const selectedIndex = selected.indexOf(id);
    let newSelected: (string | number)[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }

    setSelected(newSelected);
  }

  function checkIsSelected(id: string | number): boolean {
    return selected.indexOf(id) !== -1;
  }

  function handleDelete(): void {
    console.log("table selected", selected);
    if (props.onDelete) {
      props.onDelete(selected);
    }
    setSelected([]);
  }

  function handleRequestSort(label: string): void {
    const isAsc = orderBy === label && order === ASC;
    setOrder(isAsc ? DESC : ASC);
    setOrderBy(label);
    // TODO: Implement sorting functionality
  }

  function random(): string {
    return Math.random().toString();
  }

  return (
    <Paper className="root">
      <EnhancedTableToolbar
        numSelected={selected.length}
        raiseDelete={handleDelete}
      />
      <>
        <Table className="table">
          <TableHead>
            <TableRow>
              <TableCell key={"selectHead"}></TableCell>
              {headers
                ? headers.map((header) => (
                    <TableCell key={header.path || header.id || random()}>
                      {props.hasOrdering ? (
                        <TableSortLabel
                          active={orderBy === header.id}
                          direction={orderBy === header.id ? order : ASC}
                          onClick={() => handleRequestSort(header.id || "")}
                        >
                          {header.label}
                        </TableSortLabel>
                      ) : (
                        header.label
                      )}
                    </TableCell>
                  ))
                : ""}
            </TableRow>
          </TableHead>
          <TableBody>
            {data &&
              data.map((item) => {
                const isSelected = checkIsSelected(
                  item.id || item.msisdn || item.externalId || ""
                );
                return (
                  <TableRow
                    key={createKey(item, "row")}
                    hover
                    role="checkbox"
                    aria-checked={isSelected}
                    tabIndex={-1}
                    selected={isSelected}
                    className="tableRow"
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        onClick={(event) =>
                          handleClick(
                            event as React.MouseEvent<HTMLButtonElement>,
                            item.id || item.msisdn || item.externalId || ""
                          )
                        }
                        className="selectCheckbox"
                        checked={isSelected}
                      />
                    </TableCell>
                    {headers &&
                      headers.map((header) => (
                        <TableCell
                          key={createKey(item, header)}
                          onClick={() => handleRowClick(item)}
                        >
                          {header.content
                            ? header.content(
                                header.path ? item[header.path] : item,
                                data
                              )
                            : renderCell(item, header)}
                        </TableCell>
                      ))}
                  </TableRow>
                );
              })}
          </TableBody>
        </Table>
      </>
    </Paper>
  );
};

export default SimpleTable;
