import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { RoutingClass } from "@pnmui/common/types/smscTypes";

export const SmscRoutingClassesService = {
  getRoutingClasses(): Promise<RoutingClass[]> {
    return new Promise((resolve, reject) => {
      if (
        SecurityService.checkPermission("SMSC_ROUTING_CLASS_READ_PERMISSION")
      ) {
        HttpService.get<RoutingClass[]>(
          `${
            $properties.value["client.smsws.base.url"] ||
            "http://localhost:3500"
          }/mORoutingClasses`,
          {}
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_ROUTING_CLASS_READ_PERMISSION is required.");
        resolve([]);
      }
    });
  },

  saveRoutingClass(routingClass: RoutingClass): Promise<RoutingClass | []> {
    return new Promise((resolve, reject) => {
      const permission = routingClass.id
        ? "SMSC_ROUTING_CLASS_UPDATE_PERMISSION"
        : "SMSC_ROUTING_CLASS_CREATE_PERMISSION";

      if (SecurityService.checkPermission(permission)) {
        HttpService[routingClass.id ? "put" : "post"]<RoutingClass>(
          `${
            $properties.value["client.smsws.base.url"] ||
            "http://localhost:3500"
          }/mORoutingClasses${routingClass.id ? "/" + routingClass.id : ""}`,
          {},
          routingClass
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error(`${permission} is required.`);
        resolve([]);
      }
    });
  },

  deleteRoutingClassById(id: string | number): Promise<boolean | []> {
    return new Promise((resolve, reject) => {
      if (
        SecurityService.checkPermission("SMSC_ROUTING_CLASS_DELETE_PERMISSION")
      ) {
        HttpService.delete(
          `${
            $properties.value["client.smsws.base.url"] ||
            "http://localhost:3500"
          }/mORoutingClasses/${id}`,
          {}
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_ROUTING_CLASS_DELETE_PERMISSION is required.");
        resolve([]);
      }
    });
  },

  saveRoutingClassWithName(routingClassName: string): Promise<RoutingClass> {
    return new Promise((resolve, reject) => {
      HttpService.post<RoutingClass>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/mORoutingClasses/createWithName`,
        {},
        { name: routingClassName }
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  getRoutingClassById(id: string | number): Promise<RoutingClass> {
    return new Promise((resolve, reject) => {
      HttpService.get<RoutingClass>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/mORoutingClasses/${id ? id : ""}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};


