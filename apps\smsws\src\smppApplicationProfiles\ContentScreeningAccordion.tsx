import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Paper,
  TextField,
  Typography,
  AutocompleteChangeReason,
  AutocompleteInputChangeReason
} from "@mui/material";
import React, { useEffect, useState, MouseEvent, ChangeEvent, SyntheticEvent } from "react";
import { SmscSpamWordsService } from "@pnmui/common/services/smsc/spamWordsService";
import { ApplicationProfile, SpamWordRegistry } from "../types";

interface ContentScreeningSettings {
  enabled?: boolean;
  spamWordRegistry?: string | number;
  spamWordThreshold?: number;
  rejectOnThresholdExceeded?: boolean;
}

interface ContentScreeningAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  spamWordRegistries: SpamWordRegistry[];
  setSpamWordRegistries: React.Dispatch<React.SetStateAction<SpamWordRegistry[]>>;
  errors: Record<string, any>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

const ContentScreeningAccordion: React.FC<ContentScreeningAccordionProps> = ({
  applicationProfile,
  setApplicationProfile,
  spamWordRegistries,
  setSpamWordRegistries,
  errors,
  setErrors
}) => {
  const [spamWordRegistryInputValue, setSpamWordRegistryInputValue] =
    useState<string>("");
  const [expanded, setExpanded] = useState<boolean>(false);
  const [selectedRegistry, setSelectedRegistry] = useState<SpamWordRegistry | null>(null);

  useEffect(() => {
    setExpanded(applicationProfile.contentScreening?.enabled || false);
  }, [applicationProfile.contentScreening?.enabled]);

  useEffect(() => {
    const selected = spamWordRegistries.find(
      (registry) =>
        registry.id === applicationProfile.contentScreening?.spamWordRegistry
    );
    setSelectedRegistry(selected || null);
  }, [
    spamWordRegistries,
    applicationProfile.contentScreening?.spamWordRegistry,
  ]);

  const handleContentScreeningChange = (field: string, value: any): void => {
    setApplicationProfile((prev) => ({
      ...prev,
      contentScreening: {
        ...prev.contentScreening,
        [field]: value,
      },
    }));
  };

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.contentScreening?.enabled;
    setApplicationProfile((prev) => ({
      ...prev,
      contentScreening: {
        ...prev.contentScreening,
        enabled: newEnabled,
      },
    }));
    setExpanded(newEnabled);

    // Clear errors when disabled
    if (!newEnabled && setErrors) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.contentScreening;
        return newErrors;
      });
    }
  };

  const addNewSpamWordRegistry = async (): Promise<void> => {
    if (!spamWordRegistryInputValue) return;

    try {
      const newRegistry = { name: spamWordRegistryInputValue };
      const savedRegistry =
        await SmscSpamWordsService.saveSpamWordRegistryWithName(newRegistry);

      setSpamWordRegistries((prev) => [...prev, savedRegistry]);

      setApplicationProfile((prev) => ({
        ...prev,
        contentScreening: {
          ...prev.contentScreening,
          spamWordRegistry: savedRegistry.id,
        },
      }));

      setSpamWordRegistryInputValue(savedRegistry.name);
      setSelectedRegistry(savedRegistry);
    } catch (error: any) {
      console.error("Error creating new spam word registry:", error);
    }
  };

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{
              color: applicationProfile.contentScreening?.enabled
                ? "#3f51b5"
                : "gray",
              fontSize: "3em",
            }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable Content Screening</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{
          justifyContent: "start",
          flexDirection: "column",
          width: "100%",
        }}
      >
        <Autocomplete
          onChange={(e: SyntheticEvent, value: SpamWordRegistry | null) => {
            handleContentScreeningChange('spamWordRegistry', value ? value.id : null);
            setSelectedRegistry(value);
          }}
          options={spamWordRegistries}
          getOptionLabel={(option: SpamWordRegistry) => option.name || ""}
          value={selectedRegistry}
          inputValue={spamWordRegistryInputValue}
          onInputChange={(event: SyntheticEvent, newInputValue: string) => {
            setSpamWordRegistryInputValue(newInputValue);
          }}
          style={{ width: "25em" }}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Spam Word Registry"
              error={!!errors?.contentScreening?.spamWordRegistry}
              helperText={errors?.contentScreening?.spamWordRegistry}
            />
          )}
          PaperComponent={({ children }) => (
            <Paper>
              {children}
              <Button
                color="primary"
                fullWidth
                disabled={!spamWordRegistryInputValue}
                sx={{ justifyContent: "flex-start", pl: 2 }}
                onMouseDown={addNewSpamWordRegistry}
              >
                + Add New
              </Button>
            </Paper>
          )}
        />
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            flexFlow: "wrap",
            marginTop: "1em",
          }}
        >
          <span>
            <span style={{ color: "red" }}>If</span> the SMS contains spam words
            with a severity rating greater than{" "}
          </span>
          <TextField
            onChange={(e) => {
              handleContentScreeningChange('severityThreshold', e.target.value);
            }}
            size={"small"}
            name="contentScreening.severityThreshold"
            type="number"
            value={applicationProfile.contentScreening?.severityThreshold || 0}
            variant="outlined"
            className={"small-input"}
            error={!!errors?.contentScreening?.severityThreshold}
            helperText={''}
            required={applicationProfile.contentScreening?.enabled}
          />
          <span>using the selected Spam Word Registry, </span>
        </div>
        <p>
          <span style={{ color: "red" }}>Then</span> Block the SMS
        </p>
      </AccordionDetails>
    </Accordion>
  );
};

export default ContentScreeningAccordion;



