import { DeliveryAndRetryProfile } from "@pnmui/common/types/smscTypes";
export declare const SmscDeliveryAndRetryProfilesService: {
    getDeliveryAndRetryProfiles(): Promise<DeliveryAndRetryProfile[]>;
    saveRetryProfileWithName(retryProfileName: string): Promise<DeliveryAndRetryProfile>;
    getRetryProfileById(id: string | number): Promise<DeliveryAndRetryProfile>;
    saveRetryProfile(retryProfile: DeliveryAndRetryProfile): Promise<DeliveryAndRetryProfile>;
    deleteRetryProfileById(id: string | number): Promise<boolean>;
};
