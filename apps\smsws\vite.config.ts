import react from '@vitejs/plugin-react'
import * as path from 'path'
import { fileURLToPath } from 'url'
import { defineConfig, loadEnv } from 'vite'
import { kendoReactFix } from './vite-kendo-plugin'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')

  console.log('vite.config.ts - NODE_ENV:', env.NODE_ENV)
  console.log('vite.config.ts - REACT_APP_PROXY_URL:', env.REACT_APP_PROXY_URL)

  // Ensure REACT_APP_PROXY_URL has a value
  if (!env.REACT_APP_PROXY_URL) {
    console.log('vite.config.ts - REACT_APP_PROXY_URL not set, using default')
    env.REACT_APP_PROXY_URL = 'http://localhost:8080'
  }

  const targetUrl = env.REACT_APP_PROXY_URL

  console.log('vite.config.ts - Proxy target URL:', targetUrl)

  return {
    plugins: [react(), kendoReactFix()],
    resolve: {
      alias: {
        '@pnmui/common': path.resolve(__dirname, '../../packages/common/src'),
        '@pnmui/common/*': path.resolve(__dirname, '../../packages/common/src/*')
      }
    },
    define: {
      // Define process.env for the browser environment
      'process.env': {
        NODE_ENV: JSON.stringify(env.NODE_ENV),
        REACT_APP_PROXY_URL: JSON.stringify(env.REACT_APP_PROXY_URL),
        REACT_APP_MOCK: JSON.stringify('false'),
        STANDALONE: JSON.stringify('true'),
        PMI_URL: JSON.stringify('http://localhost:8080/pmi')
      },
      // Also expose environment variables to import.meta.env
      'import.meta.env.NODE_ENV': JSON.stringify(env.NODE_ENV),
      'import.meta.env.REACT_APP_PROXY_URL': JSON.stringify(env.REACT_APP_PROXY_URL),
      'import.meta.env.REACT_APP_MOCK': JSON.stringify('false'),
      'import.meta.env.STANDALONE': JSON.stringify('true' ),
      'import.meta.env.PMI_URL': JSON.stringify('http://localhost:8080/pmi' )
    },
    server: {
      proxy: env.STANDALONE === 'false' ? {
      '/routeRequest': {
          target: targetUrl,
          changeOrigin: true,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.error('Proxy error:', err)
              // Let the error propagate correctly
              if (!res.headersSent) {
                res.writeHead(500, { 'Content-Type': 'application/json' })
                res.end(JSON.stringify({ error: err.message || 'Proxy error' }))
              }
            })
          }
        },
        '/routeMultipartRequest': {
          target: targetUrl,
          changeOrigin: true
        },
        '/pmi': {
          target: targetUrl,
          changeOrigin: true,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.error('Proxy error for /pmi:', err)
              // Let the error propagate correctly
              if (!res.headersSent) {
                res.writeHead(500, { 'Content-Type': 'application/json' })
                res.end(JSON.stringify({ error: err.message || 'Proxy error' }))
              }
            })
          }
        }
      } : undefined
    }
  }
})
