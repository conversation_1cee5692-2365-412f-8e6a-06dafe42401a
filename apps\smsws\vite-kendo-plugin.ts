import { Plugin } from 'vite';
import * as path from 'path';
import * as fs from 'fs';

/**
 * This plugin fixes the import issues with KendoReact components
 * by transforming the imports that reference missing functions from kendo-react-common
 * and handling missing GridColumn imports
 */
export function kendoReactFix(): Plugin {
  return {
    name: 'kendo-react-fix',
    resolveId(id, importer) {
      // Handle GridColumn import issue
      if (id === '@progress/kendo-react-grid/dist/npm/GridColumn') {
        return {
          id: '@progress/kendo-react-grid',
          external: true
        };
      }
      return null;
    },
    transform(code, id) {
      // Fix for Dialog.mjs
      if (id.includes('@progress/kendo-react-dialogs/Dialog.mjs')) {
        return code.replace(
          /shouldShowValidationUI as \w+,/g,
          ''
        );
      }

      // Fix for Window.mjs
      if (id.includes('@progress/kendo-react-dialogs/Window.mjs')) {
        return code.replace(
          /shouldShowValidationUI as \w+,/g,
          ''
        );
      }

      // Fix for GridWatermarkOverlay.mjs
      if (id.includes('@progress/kendo-react-grid/GridWatermarkOverlay.mjs')) {
        return code.replace(
          /shouldShowValidationUI as \w+,\s*/g,
          ''
        );
      }

      // Fix for DropDownButton.mjs
      if (id.includes('@progress/kendo-react-buttons/ListButton/DropDownButton.mjs')) {
        return code.replace(
          /withZIndexContext as \w+,?/g,
          ''
        );
      }

      // Fix for GridColumn imports
      if (id.endsWith('.tsx') || id.endsWith('.ts')) {
        if (code.includes('@progress/kendo-react-grid/dist/npm/GridColumn')) {
          // First, check if Grid is already imported
          const hasGridImport = code.match(/import\s+{[^}]*Grid[^}]*}\s+from\s+['"]@progress\/kendo-react-grid['"];?/);

          if (hasGridImport) {
            // If Grid is already imported, just add Column as Grid.Column
            return code.replace(
              /import\s+{\s*GridColumn\s+as\s+Column\s*}\s+from\s+['"]@progress\/kendo-react-grid\/dist\/npm\/GridColumn['"];?/g,
              'const Column = Grid.Column;'
            );
          } else {
            // If Grid is not imported, import it and add Column
            return code.replace(
              /import\s+{\s*GridColumn\s+as\s+Column\s*}\s+from\s+['"]@progress\/kendo-react-grid\/dist\/npm\/GridColumn['"];?/g,
              'import { Grid } from "@progress/kendo-react-grid"; const Column = Grid.Column;'
            );
          }
        }
      }

      // Generic fix for any kendo-react module that imports from kendo-react-common
      if (id.includes('@progress/kendo-react-') && code.includes('from "@progress/kendo-react-common"')) {
        // Remove references to shouldShowValidationUI
        let modifiedCode = code.replace(/shouldShowValidationUI as \w+,?\s*/g, '');
        // Remove references to withZIndexContext
        modifiedCode = modifiedCode.replace(/withZIndexContext as \w+,?\s*/g, '');

        // Only return the modified code if changes were made
        if (modifiedCode !== code) {
          return modifiedCode;
        }
      }

      return null;
    }
  };
}
