import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { DeliveryAndRetrySchedule } from "@pnmui/common/types/smscTypes";

export const SmscDeliveryAndRetrySchedulesService = {
  getDeliveryAndRetrySchedules(): Promise<DeliveryAndRetrySchedule[]> {
    return new Promise((resolve, reject) => {
      HttpService.get<DeliveryAndRetrySchedule[]>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetrySchedules`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  getRetryScheduleById(id: string | number): Promise<DeliveryAndRetrySchedule> {
    return new Promise((resolve, reject) => {
      HttpService.get<DeliveryAndRetrySchedule>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetrySchedules/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  saveRetrySchedule(retrySchedule: DeliveryAndRetrySchedule): Promise<DeliveryAndRetrySchedule> {
    const permission = retrySchedule.id
      ? "SMSC_DELIVERY_RETRY_SCHEDULES_UPDATE_PERMISSION"
      : "SMSC_DELIVERY_RETRY_SCHEDULES_CREATE_PERMISSION";

    if (!SecurityService.checkPermission(permission)) {
      return Promise.reject(
        new Error(
          `Unauthorized: Missing ${permission.split("_")[3]} permission`
        )
      );
    }

    return new Promise((resolve, reject) => {
      HttpService[retrySchedule.id ? "put" : "post"]<DeliveryAndRetrySchedule>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetrySchedules${
          retrySchedule.id ? "/" + retrySchedule.id : ""
        }`,
        {},
        retrySchedule
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  deleteRetryScheduleById(id: string | number): Promise<boolean> {
    if (
      !SecurityService.checkPermission(
        "SMSC_DELIVERY_RETRY_SCHEDULES_DELETE_PERMISSION"
      )
    ) {
      return Promise.reject(
        new Error("Unauthorized: Missing DELETE permission")
      );
    }

    return new Promise((resolve, reject) => {
      HttpService.delete(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetrySchedules/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  saveRetryScheduleWithName(retrySchedule: { name: string }): Promise<DeliveryAndRetrySchedule> {
    return new Promise((resolve, reject) => {
      HttpService.post<DeliveryAndRetrySchedule>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetrySchedules/createWithName`,
        {},
        retrySchedule
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};


