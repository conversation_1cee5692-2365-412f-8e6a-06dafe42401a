// Define interfaces for the various objects and their validation errors

interface AdvancedProperty {
  id?: string | number;
  section: string;
  propertyName: string;
  propertyValue: string;
}

interface AdvancedPropertyErrors {
  section?: string;
  propertyName?: string;
  propertyValue?: string;
}

interface ValidationResult {
  hasErrors: boolean;
  errors: {
    [key: string]: any;
  };
}

interface AdvancedValidationResult {
  hasErrors: boolean;
  errors: {
    advanced: {
      properties: {
        [key: string]: AdvancedPropertyErrors;
      };
    };
  };
}

interface RetriesSettings {
  enabled?: boolean;
  overrideMessagePriority?: boolean;
  priorityIfNoneSpecified?: string;
  alwaysUsePriority?: string;
  lowPriorityDeliveryAndRetryProfile?: any;
  regularPriorityDeliveryAndRetryProfile?: any;
  urgentPriorityDeliveryAndRetryProfile?: any;
  emergencyPriorityDeliveryAndRetryProfile?: any;
}

interface ContentScreeningSettings {
  enabled?: boolean;
  spamWordRegistry?: any;
  severityThreshold?: number | string;
}

interface ChargingSettings {
  enabled?: boolean;
  whenToCharge?: string;
}

interface StorageSettings {
  enabled?: boolean;
  storageResourcePolicy?: any;
  consumerResourcePolicy?: any;
}

interface ThrottlingSettings {
  enabled?: boolean;
  maxMessages?: number;
  accuracy?: number;
  analysisWindow?: number;
  timeWindow?: number;
}

interface BlackoutDuration {
  type?: string;
  startTime?: string;
  endTime?: string;
  start?: string;
  end?: string;
  startDate?: string;
  endDate?: string;
  daysActive?: string[];
}

interface BlackoutPeriod extends BlackoutDuration {
  id?: string | number;
  name: string;
}

interface BlackoutSettings {
  enabled?: boolean;
  periods?: BlackoutPeriod[];
}

interface ApplicationProfile {
  id?: string | number;
  name?: string;
  description?: string;
  systemId?: string;
  password?: string;
  ipList?: any;
  maxBinds?: number;
  advanced?: {
    enabled: boolean;
    properties: AdvancedProperty[];
  };
  blackouts?: BlackoutSettings;
  charging?: ChargingSettings;
  contentScreening?: ContentScreeningSettings;
  retries?: RetriesSettings;
  storage?: StorageSettings;
  throttling?: ThrottlingSettings;
  [key: string]: any;
}

interface Enumeration {
  value: string;
  label: string;
}

interface Enumerations {
  chargingStrategy?: Enumeration[];
  [key: string]: Enumeration[] | undefined;
}

export function validateAdvancedProperty(property: AdvancedProperty): AdvancedPropertyErrors {
  const errors: AdvancedPropertyErrors = {};
  
  if (!property.section) {
    errors.section = 'Section is required';
  }
  
  if (!property.propertyName?.trim()) {
    errors.propertyName = 'Property name is required';
  }
  
  if (!property.propertyValue?.trim()) {
    errors.propertyValue = 'Property value is required';
  }
  
  return errors;
}

export function validateAdvancedProperties(properties: AdvancedProperty[]): AdvancedValidationResult {
  let hasErrors = false;
  const result: AdvancedValidationResult = {
    hasErrors: false,
    errors: {
      advanced: {
        properties: {}
      }
    }
  };

  properties.forEach(property => {
    const propertyErrors = validateAdvancedProperty(property);
    if (Object.keys(propertyErrors).length > 0) {
      result.errors.advanced.properties[property.id as string] = propertyErrors;
      hasErrors = true;
    }
  });

  result.hasErrors = hasErrors;
  return result;
}

export const validateRetries = (retries: RetriesSettings): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    console.log('Validating retries:', retries);

    if (!retries?.overrideMessagePriority) {
        // When override is disabled, check priorityIfNoneSpecified
        if (!retries?.priorityIfNoneSpecified) {
            errors.defaultPriority = 'Default Priority is required when Override Message Priority is disabled.';
        } else {
            if (retries.priorityIfNoneSpecified === "LOW" && !retries.lowPriorityDeliveryAndRetryProfile) {
                errors.lowPriorityDeliveryAndRetryProfile = 'Low Priority Delivery and Retry Profile is required when Default Priority is set to LOW';
            }
            if (retries.priorityIfNoneSpecified === "REGULAR" && !retries.regularPriorityDeliveryAndRetryProfile) {
                errors.regularPriorityDeliveryAndRetryProfile = 'Regular Priority Delivery and Retry Profile is required when Default Priority is set to REGULAR';
            }
            if (retries.priorityIfNoneSpecified === "URGENT" && !retries.urgentPriorityDeliveryAndRetryProfile) {
                errors.urgentPriorityDeliveryAndRetryProfile = 'Urgent Priority Delivery and Retry Profile is required when Default Priority is set to URGENT';
            }
            if (retries.priorityIfNoneSpecified === "EMERGENCY" && !retries.emergencyPriorityDeliveryAndRetryProfile) {
                errors.emergencyPriorityDeliveryAndRetryProfile = 'Emergency Priority Delivery and Retry Profile is required when Default Priority is set to EMERGENCY';
            }
        }
    } else {
        // When override is enabled, check alwaysUsePriority
        if (retries?.alwaysUsePriority === null || retries?.alwaysUsePriority === undefined || retries?.alwaysUsePriority === "") {
            errors.overridePriority = 'Priority must be selected when Override Message Priority is enabled';
        }
        if (retries.alwaysUsePriority === "LOW" && !retries.lowPriorityDeliveryAndRetryProfile) {
          errors.lowPriorityDeliveryAndRetryProfile = 'Low Priority Delivery and Retry Profile is required when Always Use Priority is set to LOW';
      }
      if (retries.alwaysUsePriority === "REGULAR" && !retries.regularPriorityDeliveryAndRetryProfile) {
          errors.regularPriorityDeliveryAndRetryProfile = 'Regular Priority Delivery and Retry Profile is required when Always Use Priority is set to REGULAR';
      }
      if (retries.alwaysUsePriority === "URGENT" && !retries.urgentPriorityDeliveryAndRetryProfile) {
          errors.urgentPriorityDeliveryAndRetryProfile = 'Urgent Priority Delivery and Retry Profile is required when Always Use Priority is set to URGENT';
      }
      if (retries.alwaysUsePriority === "EMERGENCY" && !retries.emergencyPriorityDeliveryAndRetryProfile) {
          errors.emergencyPriorityDeliveryAndRetryProfile = 'Emergency Priority Delivery and Retry Profile is required when Always Use Priority is set to EMERGENCY';
      }
    }

    console.log('Validation errors:', errors);
    return errors;
};

export const validateContentScreening = (contentScreening: ContentScreeningSettings): Record<string, string> => {
  const validationErrors: Record<string, string> = {};

  if (contentScreening?.enabled) {
    if (!contentScreening.spamWordRegistry) {
      validationErrors.spamWordRegistry = "Spam word registry is required when content screening is enabled";
    }
    if (contentScreening.severityThreshold === undefined || 
        contentScreening.severityThreshold === null || 
        contentScreening.severityThreshold === '') {
      validationErrors.severityThreshold = "Severity threshold is required when content screening is enabled";
    } else {
      const threshold = Number(contentScreening.severityThreshold);
      if (isNaN(threshold) || threshold < 0) {
        validationErrors.severityThreshold = "Severity threshold must be a valid positive number";
      }
    }
  }

  return validationErrors;
};

export const validateCharging = (charging: ChargingSettings, chargingStrategies?: Enumeration[]): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (charging.enabled) {
    if (!validateNotNull(charging.whenToCharge)) {
      errors.whenToCharge = "When to charge is required";
    } else if (!validateEnum(charging.whenToCharge as string, (chargingStrategies || []).map(s => s.value))) {
      errors.whenToCharge = "Invalid charging strategy";
    }
  }

  return errors;
};

export const validateStorage = (storage: StorageSettings): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (storage?.enabled) {
    if (!storage.storageResourcePolicy) {
      errors.storageResourcePolicy = "Storage resource policy is required";
    }
    if (!storage.consumerResourcePolicy) {
      errors.consumerResourcePolicy = "Consumer resource policy is required";
    }
  }

  return errors;
};

export const validateThrottling = (throttling: ThrottlingSettings): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (throttling?.enabled) {
    if (!throttling.maxMessages || throttling.maxMessages <= 0) {
      errors.maxMessages = "Max messages must be greater than 0";
    }
    if (!throttling.accuracy || throttling.accuracy <= 0) {
      errors.accuracy = "Accuracy must be greater than 0";
    }
    if (!throttling.analysisWindow || throttling.analysisWindow <= 0) {
      errors.analysisWindow = "Analysis window must be greater than 0";
    }
    if (!throttling.timeWindow || throttling.timeWindow <= 0) {
      errors.timeWindow = "Time window must be greater than 0";
    }
  }

  return errors;
};

export const validateProperty = (property: AdvancedProperty): AdvancedPropertyErrors => {
  const errors: AdvancedPropertyErrors = {};
  
  if (!property.section) {
    errors.section = 'Section is required';
  }
  
  if (!property.propertyName?.trim()) {
    errors.propertyName = 'Property name is required';
  }
  
  if (!property.propertyValue?.trim()) {
    errors.propertyValue = 'Property value is required';
  }
  
  return errors;
};

export const validateBlackoutDuration = (duration: BlackoutDuration): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!duration.startTime && !duration.start) {
    errors.startTime = "Start time is required";
  }
  if (!duration.endTime && !duration.end) {
    errors.endTime = "End time is required";
  }

  if (duration.type === "fixedBlackoutDuration") {
    if (!duration.startDate) {
      errors.startDate = "Start date is required";
    }
    if (!duration.endDate) {
      errors.endDate = "End date is required";
    }
  } else if (duration.type === "weeklyBlackoutDuration") {
    if (!duration.daysActive) {
      errors.daysActive = "Active days are required";
    }
  }

  return errors;
};

export const validateBlackoutPeriod = (period: BlackoutPeriod): Record<string, any> => {
  const errors: Record<string, any> = {};

  if (!period.name?.trim()) {
    errors.name = "Name is required";
  }

  if (period.start && period.end) {
    const durationErrors = validateBlackoutDuration(period);
    if (Object.keys(durationErrors).length > 0) {
      errors.duration = durationErrors;
    }
  } else {
    errors.duration = "Duration is required";
  }

  return errors;
};

export const validateBlackouts = (blackouts: BlackoutSettings): Record<string, any> => {
  const errors: Record<string, any> = {};

  if (blackouts?.enabled && blackouts.periods?.length > 0) {
    const periodErrors: Record<string, any> = {};
    blackouts.periods.forEach((period) => {
      const periodValidation = validateBlackoutPeriod(period);
      if (Object.keys(periodValidation).length > 0) {
        periodErrors[period.id as string] = periodValidation;
      }
    });
    
    if (Object.keys(periodErrors).length > 0) {
      errors.periods = periodErrors;
    }
  } 
  else if (blackouts?.enabled && (!blackouts.periods || blackouts.periods.length === 0)) {
    errors.periods = "At least one blackout period is required when enabled.";
  }

  return errors;
};

export function validateApplicationProfile(applicationProfile: ApplicationProfile, enumerations?: Enumerations): ValidationResult {
  let result: ValidationResult = {
    hasErrors: false,
    errors: {}
  };

  if (applicationProfile.advanced?.enabled && applicationProfile.advanced?.properties?.length > 0) {
    const advancedValidation = validateAdvancedProperties(applicationProfile.advanced.properties);
    if (advancedValidation.hasErrors) {
      result.hasErrors = true;
      result.errors.advanced = advancedValidation.errors;
    }
  }

  const retriesErrors = validateRetries(applicationProfile.retries || {});
  if (Object.keys(retriesErrors).length > 0) {
    result.hasErrors = true;
    result.errors.retries = retriesErrors;
  }

  const contentScreeningErrors = validateContentScreening(applicationProfile.contentScreening || {});
  if (Object.keys(contentScreeningErrors).length > 0) {
    result.hasErrors = true;
    result.errors.contentScreening = contentScreeningErrors;
  }

  const chargingErrors = validateCharging(applicationProfile.charging || {}, enumerations?.chargingStrategy);
  if (Object.keys(chargingErrors).length > 0) {
    result.hasErrors = true;
    result.errors.charging = chargingErrors;
  }

  const storageErrors = validateStorage(applicationProfile.storage || {});
  if (Object.keys(storageErrors).length > 0) {
    result.hasErrors = true;
    result.errors.storage = storageErrors;
  }

  const throttlingErrors = validateThrottling(applicationProfile.throttling || {});
  if (Object.keys(throttlingErrors).length > 0) {
    result.hasErrors = true;
    result.errors.throttling = throttlingErrors;
  }

  const blackoutsErrors = validateBlackouts(applicationProfile.blackouts || {});
  if (Object.keys(blackoutsErrors).length > 0) {
    result.hasErrors = true;
    result.errors.blackouts = blackoutsErrors;
  }

  return result;
}

export const validateNotNull = (value: any): boolean => {
  if (value === null || value === undefined || value === '') {
    return false;
  }
  return true;
};

export function validateEnum(value: string, allowedValues: string[]): boolean {
  return allowedValues.includes(value);
}
