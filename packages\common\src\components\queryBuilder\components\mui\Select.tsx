import { MenuItem, Select as SelectBase, SelectChangeEvent } from "@mui/material";
import styled from "styled-components";
import { ValueSelectorProps, FieldSelectorProps, OperatorSelectorProps } from "../../types";

const SelectWithBorder = styled(SelectBase)`
  border: 1px solid #dadada;
  border-radius: 5px;
  padding: 5px;
`;

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps extends ValueSelectorProps, FieldSelectorProps, OperatorSelectorProps {
  selectedValue?: string;
  values?: SelectOption[];
}

export function Select({
  handleOnChange: onChange,
  value: selectedValue,
  options: values = []
}: SelectProps) {
  const handleChange = (event: SelectChangeEvent<unknown>, _child: React.ReactNode) => {
    if (onChange) {
      onChange(event.target.value as string);
    }
  };

  // Convert options format if needed
  const selectOptions = values.map(option => {
    if ('name' in option && 'label' in option) {
      return { value: option.name, label: option.label };
    }
    return option as SelectOption;
  });

  return (
    <SelectWithBorder
      value={selectedValue || ""}
      onChange={handleChange}
      displayEmpty
    >
      <MenuItem value="" disabled>
        Select your option
      </MenuItem>
      {selectOptions.map((option) => (
        <MenuItem key={option.value} value={option.value}>
          {option.label}
        </MenuItem>
      ))}
    </SelectWithBorder>
  );
}
