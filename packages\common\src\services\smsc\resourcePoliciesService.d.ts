import { ResourcePolicy } from "@pnmui/common/types/smscTypes";
export declare const SmscResourcePoliciesService: {
    getResourcePolicyById(id: string | number): Promise<ResourcePolicy>;
    saveResourcePolicy(resourcePolicy: ResourcePolicy): Promise<ResourcePolicy>;
    getResourcePolicies(): Promise<ResourcePolicy[]>;
    deleteResourcePolicyById(id: string | number): Promise<boolean>;
    reloadResourcePolicies(host?: string): Promise<any>;
};
