/**
 * Utility class for general operations
 */
export default class GeneralUtils {
  /**
   * Deep compares two objects by converting them to JSON strings
   * @param objectA - First object to compare
   * @param objectB - Second object to compare
   * @returns True if objects are equal, false otherwise
   */
  static deepCompare<T, U>(objectA: T, objectB: U): boolean {
    return JSON.stringify(objectA) === JSON.stringify(objectB);
  }
}
