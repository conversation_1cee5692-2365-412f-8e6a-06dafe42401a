import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SpamWordRegistry } from "@pnmui/common/types/smscTypes";

export const SmscSpamWordsService = {
  getSpamWordById(id: string | number): Promise<SpamWordRegistry> {
    return new Promise((resolve, reject) => {
      HttpService.get<SpamWordRegistry>(
        `${$properties.value["client.smsws.base.url"]}/spamWordRegistries/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  saveSpamWordRegistryWithName(spamWordRegistry: { name: string }): Promise<SpamWordRegistry> {
    return new Promise((resolve, reject) => {
      HttpService.post<SpamWordRegistry>(
        `${$properties.value["client.smsws.base.url"]}/spamWordRegistries/createWithName`,
        {},
        spamWordRegistry
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  getSpamWords(): Promise<SpamWordRegistry[]> {
    return new Promise((resolve, reject) => {
      HttpService.get<SpamWordRegistry[]>(
        `${$properties.value["client.smsws.base.url"]}/spamWordRegistries`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  saveSpamWordRegistry(spamWordRegistry: SpamWordRegistry): Promise<SpamWordRegistry> {
    return new Promise((resolve, reject) => {
      const isUpdate = !!spamWordRegistry.id;
      const permission = isUpdate
        ? "SMSC_SPAM_REGISTRIES_UPDATE_PERMISSION"
        : "SMSC_SPAM_REGISTRIES_CREATE_PERMISSION";

      if (!SecurityService.checkPermission(permission)) {
        reject(
          `Permission Denied: You do not have access to ${
            isUpdate ? "update" : "create"
          } spam word registries.`
        );
        return;
      }

      HttpService[isUpdate ? "put" : "post"]<SpamWordRegistry>(
        `${$properties.value["client.smsws.base.url"]}/spamWordRegistries${
          isUpdate ? `/${spamWordRegistry.id}` : ""
        }`,
        {},
        spamWordRegistry
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  deleteSpamWordRegistry(id: string | number): Promise<any> {
    return new Promise((resolve, reject) => {
      if (
        !SecurityService.checkPermission(
          "SMSC_SPAM_REGISTRIES_DELETE_PERMISSION"
        )
      ) {
        reject("Permission Denied: Unable to delete spam word registries.");
        return;
      }
      HttpService.delete(
        `${$properties.value["client.smsws.base.url"]}/spamWordRegistries/${id}`
      )
        .then((response) => {
          if (response.status === 204 || response.status === 200) {
            resolve(response);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};


