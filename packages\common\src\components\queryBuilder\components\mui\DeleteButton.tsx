import { Button as ButtonBase } from "@mui/material";
import styled from "styled-components";
import { ActionProps } from "../../types";

const StyledButton = styled(ButtonBase)`
  white-space: nowrap;
`;

export function DeleteButton({ label, handleOnClick: onClick }: ActionProps) {
  return (
    <StyledButton onClick={onClick} color="secondary" variant="contained">
      {label}
    </StyledButton>
  );
}
