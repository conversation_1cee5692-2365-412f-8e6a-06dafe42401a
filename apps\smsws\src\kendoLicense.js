// This file is based on the official KendoReact documentation
// https://www.telerik.com/kendo-react-ui/components/my-license/

import { setScriptKey } from '@progress/kendo-licensing';

// The license key
const licenseKey = `-----BEGIN PROGRESS TELERIK LICENSE-----
eyJhbGciOiJSUzI1NiIsInR5cCI6IkxJQyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SIGNATURE_PLACEHOLDER
-----END PROGRESS TELERIK LICENSE-----`;

try {
  // Register the license
  setScriptKey('kendo-ui-license', licenseKey);
  console.log('KendoReact license registered successfully from kendoLicense.js');
} catch (error) {
  console.error('Error registering KendoReact license:', error);
}
