/**
 * Paginates an array of items
 * @param items - Array of items to paginate
 * @param pageNumber - Current page number (0-based)
 * @param pageSize - Number of items per page
 * @returns Sliced array containing only the items for the current page
 */
export const paginate = <T>(items: T[], pageNumber: number, pageSize: number): T[] =>
  items.slice(pageNumber * pageSize, (pageNumber + 1) * pageSize);
