body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.shell-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.shell-header {
  background-color: #1976d2;
  color: white;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.shell-nav {
  background-color: #f5f5f5;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #ddd;
}

.shell-nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  gap: 1rem;
}

.shell-nav li {
  margin: 0;
}

.shell-nav a {
  text-decoration: none;
  color: #1976d2;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.shell-nav a:hover {
  background-color: #e3f2fd;
}

.shell-nav a.active {
  background-color: #1976d2;
  color: white;
}

.shell-content {
  flex: 1;
  padding: 1rem;
  overflow: auto;
}
