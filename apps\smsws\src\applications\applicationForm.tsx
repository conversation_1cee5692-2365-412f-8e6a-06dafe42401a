import { SecurityService } from "@pnmui/common/services/securityService";
import { ChangeEvent, useEffect, useState } from "react";
import {
  SelectValidator,
  TextValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  FormControlLabel,
  MenuItem,
  Paper,
  Switch,
} from "@mui/material";
import {
  CommandCell,
  ErrorsDisplay,
  InputCell,
} from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SmscApplicationsService } from "@pnmui/common/services/smsc/applicationsService";
import { SmscConnectionsService } from "@pnmui/common/services/smsc/connectionsService";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import { SmscApplicationProfilesService } from "@pnmui/common/services/smsc/smppApplicationProfilesService";
import { GridItemChangeEvent, GridToolbar } from "@progress/kendo-react-grid";
import { Grid, Column } from "../components/KendoGridWrapper";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { tap } from "rxjs/operators";
import "../common.css";
import { Application } from "../types";

interface NetworkAddress {
  type: string;
  id?: number | null;
  shortCode?: string;
  shortCodeFrom?: string;
  shortCodeTo?: string;
  shortCodePrefix?: string;
  shortCodeLength?: string;
  createdTime?: Date | null;
  lastUpdatedTime?: Date | null;
}

interface A2PMessageHandling {
  enabled: boolean;
  profile: { id: string | number; name: string } | null;
  forceImmediateDisplay: boolean;
  forceDeleteAfterDisplay: boolean;
  displayAlternativeAddress: boolean;
  alternativeAddress: string;
}

interface P2AMessageHandling {
  enabled: boolean;
  sendTo: { id: string | number; name: string } | null;
}

interface AdvancedSettings {
  properties: Array<{
    id: string | number;
    name: string;
    value: string;
    inEdit?: boolean;
    section?: string | null;
    createdTime?: Date | null;
    lastUpdatedTime?: Date | null;
  }>;
  ton: string;
  npi: string;
}

interface ApplicationModel {
  id?: string | number;
  name: string;
  networkAddress: NetworkAddress;
  a2pMessageHandling: A2PMessageHandling;
  p2aMessageHandling: P2AMessageHandling;
  advanced: AdvancedSettings;
}

interface EnumerationOption {
  value: string;
  displayText: string;
}

interface Enumerations {
  typeOfNumber?: EnumerationOption[];
  numberPlanIndicator?: EnumerationOption[];
  [key: string]: EnumerationOption[] | undefined;
}

interface PropertyErrors {
  name?: string;
  value?: string;
}

interface FormErrors {
  advanced?: {
    properties?: {
      [key: string]: PropertyErrors;
    };
  };
}

const SmscApplicationForm = () => {
  const navigate = useNavigate();
  const params = useParams<{ id: string }>();
  const [connections, setConnections] = useState<Application[]>([]);
  const [enumerations, setEnumerations] = useState<Enumerations>({});
  const [applicationProfiles, setApplicationProfiles] = useState<Application[]>(
    []
  );
  const [connectionInputValue, setConnectionInputValue] = useState<string>("");
  const [applicationProfileInputValue, setApplicationProfileInputValue] =
    useState<string>("");
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [application, setApplication] = useState<ApplicationModel>({
    name: "",
    networkAddress: {
      type: "",
      shortCode: "",
      shortCodeFrom: "",
      shortCodeTo: "",
      shortCodePrefix: "",
      shortCodeLength: "",
    },
    a2pMessageHandling: {
      enabled: false,
      profile: null,
      forceImmediateDisplay: false,
      forceDeleteAfterDisplay: false,
      displayAlternativeAddress: false,
      alternativeAddress: "",
    },
    p2aMessageHandling: {
      enabled: false,
      sendTo: null,
    },
    advanced: {
      properties: [],
      ton: "",
      npi: "",
    },
  });
  const [errorResponse, setErrorResponse] = useState<any>(null);
  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    console.log("ApplicationForm: Component mounted, params:", params);

    $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        console.log("ApplicationForm: i18n properties loaded");
        setI18n(i18NProps);
      }
    });

    SmscEnumerationsService.getEnumerations()
      .then((data) => {
        console.log("ApplicationForm: Enumerations loaded:", data);
        setEnumerations(data);
      })
      .catch((error) => {
        console.error("ApplicationForm: Error loading enumerations:", error);
      });

    if (params["id"] && params["id"] !== "new") {
      console.log(
        "ApplicationForm: Loading application with ID:",
        params["id"]
      );
      SmscApplicationsService.getApplicationById(params["id"])
        .then((data) => {
          console.log("ApplicationForm: Application data loaded:", data);
          setApplication(mapWebServiceToModel(data));
        })
        .catch((error) => {
          console.error("ApplicationForm: Error loading application:", error);
        });
    } else {
      console.log("ApplicationForm: Creating new application");
    }

    SmscConnectionsService.getConnections()
      .then((data) => {
        console.log("ApplicationForm: Connections loaded:", data);
        setConnections(data);
      })
      .catch((error) => {
        console.error("ApplicationForm: Error loading connections:", error);
      });

    SmscApplicationProfilesService.getApplicationProfiles()
      .then((data) => {
        console.log("ApplicationForm: Application profiles loaded:", data);
        setApplicationProfiles(data);
      })
      .catch((error) => {
        console.error(
          "ApplicationForm: Error loading application profiles:",
          error
        );
      });
  }, [params]);

  useEffect(() => {
    updateTonNpiBasedOnShortcode();
  }, [
    application.networkAddress.type,
    application.networkAddress.shortCode,
    application.networkAddress.shortCodeFrom,
    application.networkAddress.shortCodePrefix,
    application.networkAddress.shortCodeTo,
  ]);

  function mapWebServiceToModel(webServiceObject: any): ApplicationModel {
    return {
      id: webServiceObject.id,
      name: webServiceObject.name,
      networkAddress: {
        type: webServiceObject.networkAddress?.type || "",
        id: webServiceObject.networkAddress?.id || null,
        shortCode: webServiceObject.networkAddress?.shortCode || "",
        shortCodeFrom: webServiceObject.networkAddress?.shortCodeFrom || "",
        shortCodeTo: webServiceObject.networkAddress?.shortCodeTo || "",
        shortCodePrefix: webServiceObject.networkAddress?.shortCodePrefix || "",
        shortCodeLength: webServiceObject.networkAddress?.shortCodeLength || "",
        createdTime: webServiceObject.networkAddress?.createdTime || null,
        lastUpdatedTime:
          webServiceObject.networkAddress?.lastUpdatedTime || null,
      },
      a2pMessageHandling: {
        enabled: !!webServiceObject.a2pMessageHandlingEnabled,
        profile: webServiceObject.applicationProfile
          ? {
              id: webServiceObject.applicationProfile,
              name:
                applicationProfiles.find(
                  (profile) =>
                    profile.id === webServiceObject.applicationProfile
                )?.name || "",
            }
          : null,
        forceImmediateDisplay: webServiceObject.forceImmediateDisplay || false,
        forceDeleteAfterDisplay:
          webServiceObject.forceDeleteAfterDisplay || false,
        displayAlternativeAddress: !!webServiceObject.replacementSourceAddress,
        alternativeAddress: webServiceObject.replacementSourceAddress || "",
      },
      p2aMessageHandling: {
        enabled: webServiceObject.p2aMessageHandlingEnabled || false,
        sendTo: webServiceObject.p2aConnection
          ? {
              id: webServiceObject.p2aConnection,
              name:
                connections.find(
                  (conn) => conn.id === webServiceObject.p2aConnection
                )?.name || "",
            }
          : null,
      },
      advanced: {
        properties: webServiceObject.additionalProperties || [],
        ton: webServiceObject.networkAddress?.typeOfNumber || "",
        npi: webServiceObject.networkAddress?.numberPlanIndicator || "",
      },
    };
  }

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>
  ) => {
    const { name, value } = e.target;
    if (!name) return;

    if (name.indexOf(".") > -1) {
      const [parent, child] = name.split(".");
      setApplication((prev) => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof ApplicationModel] as any),
          [child]: value,
        },
      }));
    } else {
      setApplication((prev) => ({ ...prev, [name]: value }));
    }

    /*  if (
      name === "networkAddress.shortCode" ||
      name === "networkAddress.shortCodeFrom" ||
      name === "networkAddress.shortCodePrefix"
    ) {
      updateTonNpiBasedOnShortcode();
    } */
  };
  function updateTonNpiBasedOnShortcode(): void {
    const { shortCode, shortCodeFrom, shortCodePrefix } =
      application.networkAddress;
    const valueToCheck = (
      shortCode ||
      shortCodeFrom ||
      shortCodePrefix ||
      ""
    ).trim();
    const isNumeric = /^\d+$/.test(valueToCheck);

    let newTon = "";
    let newNpi = "";

    if (valueToCheck.length > 0) {
      if (isNumeric) {
        // All digits
        newTon = "UNKNOWN";
        newNpi = "ISDN";
      } else {
        newTon = "ALPHANUMERIC";
        newNpi = "UNKNOWN";
      }
    } else {
      newTon = "";
      newNpi = "";
    }

    setApplication((prev) => ({
      ...prev,
      advanced: { ...prev.advanced, ton: newTon, npi: newNpi },
    }));
  }
  const handleSubmit = (): void => {
    let hasValidationErrors = false;
    const newErrors: FormErrors = { advanced: { properties: {} } };

    // Validate advanced properties
    if (application.advanced?.properties?.length > 0) {
      application.advanced.properties.forEach((property) => {
        const propertyErrors = validateAdvancedProperty(property);
        if (Object.keys(propertyErrors).length > 0) {
          if (newErrors.advanced?.properties) {
            newErrors.advanced.properties[property.id] = propertyErrors;
          }
          hasValidationErrors = true;
        }
      });
    }

    if (hasValidationErrors) {
      setErrors(newErrors);
      toast.error("Please fix validation errors before submitting");
      return;
    }

    console.log("handleSubmit application", application);
    SmscApplicationsService.saveApplication(mapModelToWebService(application))
      .then((data) => {
        console.log("handleSubmit data", data);
        toast.success("Application saved successfully");
        navigate("/smscApplications");
      })
      .catch((error) => {
        console.error("handleSubmit error", error);
        setErrorResponse(error);
      });
  };

  function goBack(): void {
    navigate(-1);
  }

  const addNew = (): void => {
    const newProperty = {
      id: `temp_${Date.now()}`,
      name: "",
      value: "",
      inEdit: true,
    };

    // Turn off edit mode for all other properties
    const updatedProperties = (application.advanced?.properties || []).map(prop => ({
      ...prop,
      inEdit: false
    }));

    setApplication((prevApplication) => ({
      ...prevApplication,
      advanced: {
        ...prevApplication.advanced,
        properties: [
          newProperty,
          ...updatedProperties,
        ],
      },
    }));
  };

  const validateAdvancedProperty = (property: {
    name?: string;
    value?: string;
  }): PropertyErrors => {
    const errors: PropertyErrors = {};

    if (!property.name?.trim()) {
      errors.name = "Property name is required";
    }

    if (!property.value?.trim()) {
      errors.value = "Property value is required";
    }

    return errors;
  };

  const getErrorMessage = (
    propertyId: string | number,
    field: string
  ): string | undefined => {
    return errors?.advanced?.properties?.[propertyId]?.[
      field as keyof PropertyErrors
    ];
  };

  const onItemChange = (event: GridItemChangeEvent) => {
    const { dataItem, field, value } = event;

    setApplication((prevApplication) => {
      const properties = prevApplication.advanced?.properties || [];
      const propertyIndex = properties.findIndex((p) => p.id === dataItem.id);

      if (propertyIndex === -1) return prevApplication;

      const updatedProperties = [...properties];
      updatedProperties[propertyIndex] = {
        ...updatedProperties[propertyIndex],
        [field]: value,
        inEdit: true,
      };

      // Clear any validation errors for this field
      if (errors?.advanced?.properties?.[dataItem.id]?.[field]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          if (newErrors.advanced?.properties?.[dataItem.id]) {
            const propErrors = { ...newErrors.advanced.properties[dataItem.id] };
            delete propErrors[field];

            if (Object.keys(propErrors).length === 0) {
              delete newErrors.advanced.properties[dataItem.id];
            } else {
              newErrors.advanced.properties[dataItem.id] = propErrors;
            }
          }
          return newErrors;
        });
      }

      return {
        ...prevApplication,
        advanced: {
          ...prevApplication.advanced,
          properties: updatedProperties,
        },
      };
    });
  };

  function mapModelToWebService(application: ApplicationModel): any {
    const webServiceObject: any = {};

    // General properties (always included)
    if (application.id !== undefined) webServiceObject.id = application.id;
    if (application.name) webServiceObject.name = application.name;

    // Network Address Handling (always included since it's required)
    if (application.networkAddress?.type) {
      const {
        type,
        shortCode,
        shortCodeFrom,
        shortCodeTo,
        shortCodePrefix,
        shortCodeLength,
      } = application.networkAddress;

      webServiceObject.networkAddress = { type };

      if (shortCode) webServiceObject.networkAddress.shortCode = shortCode;
      if (shortCodeFrom)
        webServiceObject.networkAddress.shortCodeFrom = shortCodeFrom;
      if (shortCodeTo)
        webServiceObject.networkAddress.shortCodeTo = shortCodeTo;
      if (shortCodePrefix)
        webServiceObject.networkAddress.shortCodePrefix = shortCodePrefix;
      if (shortCodeLength)
        webServiceObject.networkAddress.shortCodeLength = shortCodeLength;

      // TON and NPI Overrides (only if network address exists)
      if (application.advanced.ton) {
        webServiceObject.networkAddress.typeOfNumber = application.advanced.ton;
      }
      if (application.advanced.npi) {
        webServiceObject.networkAddress.numberPlanIndicator =
          application.advanced.npi;
      }
    }

    // A2P Message Handling (only include if enabled)
    if (application.a2pMessageHandling.enabled) {
      webServiceObject.a2pMessageHandlingEnabled = true;
      webServiceObject.applicationProfile =
        application.a2pMessageHandling.profile?.id || null;
      webServiceObject.replacementSourceAddress = application.a2pMessageHandling
        .displayAlternativeAddress
        ? application.a2pMessageHandling.alternativeAddress
        : null;
    } else {
      webServiceObject.a2pMessageHandlingEnabled = false;
      webServiceObject.applicationProfile = null;
      webServiceObject.replacementSourceAddress = null;
    }

    // P2A Message Handling (only include if enabled)
    if (application.p2aMessageHandling.enabled) {
      webServiceObject.p2aMessageHandlingEnabled = true;
      webServiceObject.p2aConnection =
        application.p2aMessageHandling.sendTo?.id || null;
    } else {
      webServiceObject.p2aMessageHandlingEnabled = false;
      webServiceObject.p2aConnection = null;
    }

    // Advanced Properties (only include if there are properties)
    if (
      application.advanced.properties &&
      application.advanced.properties.length > 0
    ) {
      webServiceObject.additionalProperties = application.advanced.properties
        .filter((prop) => prop.name && prop.value) // Only include properties with both name and value
        .map((prop) => ({
          id:
            prop.id && typeof prop.id === "number" && !isNaN(prop.id)
              ? prop.id
              : null,
          name: prop.name,
          value: prop.value,
          section: prop.section || null,
          createdTime: prop.createdTime || null,
          lastUpdatedTime: prop.lastUpdatedTime || null,
        }));
    } else {
      webServiceObject.additionalProperties = [];
    }

    return webServiceObject;
  }
  function addNewConnection(): void {
    const newConnection = {
      name: connectionInputValue,
    };
    SmscConnectionsService.saveConnectionWithName(newConnection).then(
      (data: Application) => {
        setConnections([...connections, data]);
        setApplication({
          ...application,
          p2aMessageHandling: {
            ...application.p2aMessageHandling,
            sendTo: { id: data.id!, name: data.name! },
          },
        });
        setConnectionInputValue(data.name!);
      }
    );
  }

  function addNewApplicationProfile(): void {
    const newApplicationProfile = {
      name: applicationProfileInputValue,
    };
    SmscApplicationProfilesService.saveSmppApplicationProfileWithName(
      newApplicationProfile
    ).then((data: Application) => {
      setApplicationProfiles([...applicationProfiles, data]);
      setApplication({
        ...application,
        a2pMessageHandling: {
          ...application.a2pMessageHandling,
          profile: { id: data.id!, name: data.name! },
        },
      });
    });
  }

  return (
    <div style={{ padding: "0.5em" }}>
      <ValidatorForm onSubmit={handleSubmit} className="tango-form">
        <div style={{ marginLeft: "1em" }}>
          <Card>
            <CardContent>
              <ErrorsDisplay
                errorResponse={errorResponse}
                keyPrefix={"smsws.application.form.keys"}
              />
              <TextValidator
                label={i18n["smsc.resourcePolicies.list.name"] || "Name"}
                onChange={handleChange}
                name="name"
                value={application.name}
                validators={["required"]}
                errorMessages={i18n["spcm.name.required"] || "Name is required"}
              />
              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <span className={"accordion-title"}>
                    {
                      i18n[
                        "smsws.application.form.keys.networkAddress" ||
                          "Network Address"
                      ]
                    }
                  </span>
                </AccordionSummary>
                <AccordionDetails>
                  <div
                    className="flex-container"
                    style={{ justifyContent: "start" }}
                  >
                    <SelectValidator
                      label={
                        i18n["spcm.networkAddress.label"] ||
                        "Network Address Type"
                      }
                      onChange={handleChange}
                      name="networkAddress.type"
                      validators={["required"]}
                      errorMessages={
                        i18n["spcm.networkAddress.required"] ||
                        "Network Address Type is required"
                      }
                      value={application.networkAddress?.type || ""}
                    >
                      <MenuItem value={"networkAddressShortCode"}>
                        {i18n["smsc.applicationForm.accordion.sortcode"] ||
                          "Shortcode"}
                      </MenuItem>
                      <MenuItem value={"networkAddressShortCodeRange"}>
                        {i18n["smsc.applicationForm.accordion.sortcoderange"] ||
                          "Shortcode Range"}
                      </MenuItem>
                      <MenuItem
                        value={"networkAddressShortCodePrefixAndLength"}
                      >
                        {i18n[
                          "smsc.applicationForm.accordion.sortcodePrefix"
                        ] || "Shortcode Prefix and Length"}
                      </MenuItem>
                    </SelectValidator>

                    {application.networkAddress?.type ===
                      "networkAddressShortCode" && (
                      <>
                        <TextValidator
                          label={i18n["spcm.shortCode.label"] || "ShortCode"}
                          onChange={handleChange}
                          name="networkAddress.shortCode"
                          value={application.networkAddress?.shortCode || ""}
                          validators={["required"]}
                          errorMessages={
                            i18n["spcm.shortCode.required"] ||
                            "Short code is required"
                          }
                        />
                      </>
                    )}

                    {application.networkAddress?.type ===
                      "networkAddressShortCodeRange" && (
                      <>
                        <TextValidator
                          label={
                            i18n["spcm.shortCodeFrom.label"] || "ShortCode From"
                          }
                          type={"number"}
                          onChange={handleChange}
                          name="networkAddress.shortCodeFrom"
                          value={
                            application.networkAddress?.shortCodeFrom || ""
                          }
                          validators={["required"]}
                          errorMessages={
                            i18n["spcm.shortCodefrom.required"] ||
                            "Short code from is required"
                          }
                        />
                        <TextValidator
                          label={
                            i18n["spcm.shortCodeTo.label"] || "ShortCode To"
                          }
                          type={"number"}
                          onChange={handleChange}
                          name="networkAddress.shortCodeTo"
                          value={application.networkAddress?.shortCodeTo || ""}
                          validators={["required"]}
                          errorMessages={
                            i18n["spcm.shortCodeTo.required"] ||
                            "Short code to is required"
                          }
                        />
                      </>
                    )}

                    {application.networkAddress?.type ===
                      "networkAddressShortCodePrefixAndLength" && (
                      <>
                        <TextValidator
                          label={
                            i18n["spcm.shortCodePrefix.label"] ||
                            "ShortCode Prefix"
                          }
                          type={"text"}
                          onChange={handleChange}
                          name="networkAddress.shortCodePrefix"
                          value={
                            application.networkAddress?.shortCodePrefix || ""
                          }
                          validators={["required"]}
                          errorMessages={
                            i18n["spcm.shortCodePrefix.required"] ||
                            "Short code prefix is required"
                          }
                        />
                        <TextValidator
                          label={
                            i18n["spcm.shortCodeLength.label"] ||
                            "ShortCode Length"
                          }
                          type={"number"}
                          onChange={handleChange}
                          name="networkAddress.shortCodeLength"
                          value={
                            application.networkAddress?.shortCodeLength || ""
                          }
                          validators={[
                            "required",
                            "minNumber:1",
                            "maxNumber:50",
                          ]}
                          errorMessages={
                            (i18n["spcm.shortCodeErrors.required"] ||
                              "Short code length is required",
                            "Minimum value is 1",
                            "Maximum value is 50")
                          }
                        />
                      </>
                    )}
                  </div>

                  {(application.networkAddress?.type ===
                    "networkAddressShortCode" ||
                    application.networkAddress?.type ===
                      "networkAddressShortCodeRange" ||
                    application.networkAddress?.type ===
                      "networkAddressShortCodePrefixAndLength") && (
                    <>
                      <p style={{ marginBottom: "0.5em" }}>
                        {i18n["smsc.applicationForm.TonNpi.advanced.desc"] ||
                          "Here you can override TON and NPI that were set based on your shortcode"}
                      </p>
                      <div
                        style={{
                          display: "flex",
                          marginTop: "0.5em",
                          gap: "1em",
                        }}
                      >
                        <SelectValidator
                          label={
                            i18n["antispam.ratingRule.list.typeOfNumber"] ||
                            "Type of Number (TON)"
                          }
                          onChange={handleChange}
                          name="advanced.ton"
                          value={application.advanced.ton || ""}
                        >
                          {enumerations?.typeOfNumber?.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.displayText}
                            </MenuItem>
                          ))}
                        </SelectValidator>

                        <SelectValidator
                          label={
                            i18n["spcm.NumberingPlanIndicator"] ||
                            "Numbering Plan Indicator (NPI)"
                          }
                          onChange={handleChange}
                          name="advanced.npi"
                          value={application.advanced.npi || ""}
                        >
                          {enumerations?.numberPlanIndicator?.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.displayText}
                            </MenuItem>
                          ))}
                        </SelectValidator>
                      </div>
                    </>
                  )}
                </AccordionDetails>
              </Accordion>

              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                expanded={application.a2pMessageHandling.enabled}
              >
                <AccordionSummary
                  expandIcon={
                    <ToggleOffIcon
                      style={{
                        color: application.a2pMessageHandling.enabled
                          ? "#3f51b5"
                          : "gray",
                        fontSize: "3em",
                      }}
                      onClick={(e) => {
                        setApplication({
                          ...application,
                          a2pMessageHandling: {
                            ...application.a2pMessageHandling,
                            enabled: !application.a2pMessageHandling.enabled,
                          },
                        });
                      }}
                    />
                  }
                >
                  <span className={"accordion-title"}>
                    {i18n[
                      "smsc.applicationForm.accordion.A2PmessageHandling"
                    ] || "A2P Message Handling"}
                  </span>
                </AccordionSummary>
                <AccordionDetails
                  className="flex-container"
                  style={{ justifyContent: "start", alignItems: "center" }}
                >
                  <p>
                    {i18n["smsc.applicationForm.accordion.smsHandling"] ||
                      "Select Profile to use for handling of SMS from this Application"}
                    &nbsp;
                  </p>
                  <div className={"flex-container"}>
                    <Autocomplete
                      label={
                        i18n["smsc.applicationForm.accordion.profileTitle"] ||
                        "Application Profile"
                      }
                      onChange={(e, value) => {
                        setApplication({
                          ...application,
                          a2pMessageHandling: {
                            ...application.a2pMessageHandling,
                            profile: value
                              ? { id: value.id, name: value.name }
                              : null,
                          },
                        });
                      }}
                      options={applicationProfiles}
                      getOptionLabel={(option) => option.name || ""}
                      value={
                        applicationProfiles.find(
                          (profile) =>
                            profile.id ===
                            application.a2pMessageHandling.profile?.id
                        ) || null
                      }
                      inputValue={applicationProfileInputValue}
                      style={{ width: "24.6em" }}
                      onInputChange={(event, newInputValue) => {
                        setApplicationProfileInputValue(newInputValue);
                      }}
                      renderInput={(params) => (
                        <TextValidator
                          {...params}
                          label={
                            i18n[
                              "smsc.applicationForm.accordion.profileTitle"
                            ] || "Application Profile"
                          }
                          placeholder="Type for selecting or adding an Application Profile."
                          value={applicationProfileInputValue}
                          name="a2pMessageHandling.profile"
                          validators={
                            application.a2pMessageHandling.enabled
                              ? ["required"]
                              : []
                          }
                          errorMessages={[
                            i18n["applicationProfile.required"] ||
                              "Application Profile is required",
                          ]}
                        />
                      )}
                      PaperComponent={(e) => (
                        <Paper>
                          {e.children}
                          <Button
                            disabled={
                              !applicationProfileInputValue ||
                              !!applicationProfiles.find(
                                (a) => a.name === applicationProfileInputValue
                              )
                            }
                            color="primary"
                            fullWidth
                            sx={{ justifyContent: "flex-start", pl: 2 }}
                            onMouseDown={(ev) => {
                              addNewApplicationProfile();
                            }}
                          >
                            {i18n["smsc.application.createWithName.addNew"] ||
                              "+ Add New"}
                          </Button>
                        </Paper>
                      )}
                    />
                  </div>
                  <div
                    className={"flex-container"}
                    style={{ alignItems: "center" }}
                  >
                    <FormControlLabel
                      style={{
                        width: "25em",
                        alignItems: "center",
                        justifyContent: "start",
                        flexWrap: "wrap",
                        gap: "0.5em",
                      }}
                      control={
                        <Switch
                          checked={
                            application &&
                            application.a2pMessageHandling &&
                            application.a2pMessageHandling
                              .displayAlternativeAddress
                          }
                          onChange={(e) =>
                            setApplication({
                              ...application,
                              a2pMessageHandling: {
                                ...application.a2pMessageHandling,
                                displayAlternativeAddress: e.target.checked,
                              },
                            })
                          }
                          value={
                            application &&
                            application.a2pMessageHandling &&
                            application.a2pMessageHandling
                              .displayAlternativeAddress
                          }
                        />
                      }
                      label={
                        i18n["smsc.applicationForm.switchLabel.title"] ||
                        "Display Alternative Address on Handset"
                      }
                    />
                    {application.a2pMessageHandling
                      .displayAlternativeAddress && (
                      <>
                        <TextValidator
                          label={
                            i18n["spcm.AlternativeAddress.label"] ||
                            "Alternative Address"
                          }
                          onChange={handleChange}
                          name="a2pMessageHandling.alternativeAddress"
                          value={
                            application.a2pMessageHandling.alternativeAddress
                          }
                          validators={["required", "maxStringLength:11"]}
                          errorMessages={[
                            i18n["spcm.alternativeAddress.required"] ||
                              "Alternative Address is required",
                            i18n["spcm.alternativeAddress.maxLength"] ||
                              "Maximum 11 characters allowed",
                          ]}
                        />
                        <span>
                          {i18n["spcm.defaultAplhabet.required"] ||
                            "max 11 characters GSM default alphabet"}
                        </span>
                      </>
                    )}
                  </div>
                </AccordionDetails>
              </Accordion>

              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                expanded={application.p2aMessageHandling.enabled}
              >
                <AccordionSummary
                  expandIcon={
                    <ToggleOffIcon
                      style={{
                        color: application.p2aMessageHandling.enabled
                          ? "#3f51b5"
                          : "gray",
                        fontSize: "3em",
                      }}
                      onClick={(e) => {
                        setApplication({
                          ...application,
                          p2aMessageHandling: {
                            ...application.p2aMessageHandling,
                            enabled: !application.p2aMessageHandling.enabled,
                          },
                        });
                      }}
                    />
                  }
                >
                  <span className={"accordion-title"}>
                    {i18n["smsc.applicationForm.accordion.messageHandling"] ||
                      "P2A Message Handling"}
                  </span>
                </AccordionSummary>
                <AccordionDetails
                  className="flex-container"
                  style={{ justifyContent: "start" }}
                >
                  <Autocomplete
                    label="Send to"
                    onChange={(e, value) => {
                      setApplication({
                        ...application,
                        p2aMessageHandling: {
                          ...application.p2aMessageHandling,
                          sendTo: value
                            ? { id: value.id, name: value.name }
                            : null,
                        },
                      });
                    }}
                    options={connections}
                    getOptionLabel={(option) => option.name || ""}
                    value={
                      connections.find(
                        (conn) =>
                          conn.id === application.p2aMessageHandling.sendTo?.id
                      ) || null
                    }
                    inputValue={connectionInputValue}
                    style={{ width: "24.6em" }}
                    onInputChange={(event, newInputValue) => {
                      setConnectionInputValue(newInputValue);
                    }}
                    renderInput={(params) => (
                      <TextValidator
                        {...params}
                        label="Send to"
                        placeholder="Type for selecting or adding a Connection."
                        value={connectionInputValue}
                        name="p2aMessageHandling.sendTo"
                        validators={
                          application.p2aMessageHandling.enabled
                            ? ["required"]
                            : []
                        }
                        errorMessages={[
                          i18n["p2aConnection.required"] ||
                            "Connection is required",
                        ]}
                      />
                    )}
                    PaperComponent={(e) => (
                      <Paper>
                        {e.children}
                        <Button
                          disabled={
                            !connectionInputValue ||
                            !!connections.find(
                              (a) => a.name === connectionInputValue
                            )
                          }
                          color="primary"
                          fullWidth
                          sx={{ justifyContent: "flex-start", pl: 2 }}
                          onMouseDown={(ev) => {
                            addNewConnection();
                          }}
                        >
                          {i18n["smsc.application.createWithName.addNew"] ||
                            "+ Add New"}
                        </Button>
                      </Paper>
                    )}
                  />
                </AccordionDetails>
              </Accordion>

              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <span className={"accordion-title"}>
                    {" "}
                    {i18n["smsc.applicationForm.accordion.title"] || "Advanced"}
                  </span>
                </AccordionSummary>
                <AccordionDetails className="flex-container">
                  <div
                    style={{
                      width: "100%",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                    }}
                  >
                    <p>
                      {i18n[
                        "smsc.applicationForm.propertyvalue.advanced.properties"
                      ] ||
                        "Here you can configure additional properties. Refer to  Admin Guide for full list"}
                    </p>
                    <Grid
                      editField="inEdit"
                      editable={true}
                      onItemChange={onItemChange}
                      style={{
                        width: "60em",
                      }}
                      data={application.advanced.properties || []}
                      total={application.advanced.properties?.length || 0}
                    >
                      <GridToolbar>
                        <button
                          type={"button"}
                          className="k-primary k-button k-grid-edit-command"
                          style={{ position: "absolute", right: "1em" }}
                          onClick={addNew}
                        >
                          {i18n["button.add"] || "Add"}
                        </button>
                      </GridToolbar>
                      <Column
                        field="name"
                        title={
                          i18n["spcm.PropertyName.label"] || "Property Name"
                        }
                        cell={(props) => (
                          <InputCell
                            {...props}
                            onChange={onItemChange}
                            error={getErrorMessage(props.dataItem.id, "name")}
                            debounceDelay={500}
                          />
                        )}
                      />
                      <Column
                        field="value"
                        title={
                          i18n["spcm.PropertyValue.label"] || "Property Value"
                        }
                        cell={(props) => (
                          <InputCell
                            {...props}
                            onChange={onItemChange}
                            error={getErrorMessage(props.dataItem.id, "value")}
                            debounceDelay={500}
                          />
                        )}
                      />
                      <Column
                        cell={(props) => (
                          <CommandCell
                            {...props}
                            item={application.advanced}
                            onChange={(data) => {
                              setApplication({
                                ...application,
                                advanced: {
                                  ...application.advanced,
                                  properties: data,
                                },
                              });
                            }}
                            onSave={(dataItem) => {
                              const validationErrors =
                                validateAdvancedProperty(dataItem);
                              if (Object.keys(validationErrors).length > 0) {
                                setErrors((prev) => ({
                                  ...prev,
                                  advanced: {
                                    ...prev?.advanced,
                                    properties: {
                                      ...prev?.advanced?.properties,
                                      [dataItem.id]: validationErrors,
                                    },
                                  },
                                }));
                                toast.error(Object.values(validationErrors)[0]);
                                return false;
                              }
                              return true;
                            }}
                            gridProp={"properties"}
                          />
                        )}
                        filterable={false}
                      />
                    </Grid>
                  </div>
                </AccordionDetails>
              </Accordion>
            </CardContent>
            <CardActions className="card-actions content-card-actions">
              <Button
                variant="contained"
                color="secondary"
                type="button"
                onClick={goBack.bind(this)}
              >
                {i18n["button.back"] || "Back"}
              </Button>
              {SecurityService.checkPermission(
                "SMSC_APPLICATION_UPDATE_PERMISSION"
              ) && (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  className="request-handler-submit-button"
                >
                  {i18n["button.submit"] || "Save"}
                </Button>
              )}
            </CardActions>
          </Card>
        </div>
      </ValidatorForm>
    </div>
  );
};

export default SmscApplicationForm;
