import { SpamWordRegistry } from "@pnmui/common/types/smscTypes";
export declare const SmscSpamWordsService: {
    getSpamWordById(id: string | number): Promise<SpamWordRegistry>;
    saveSpamWordRegistryWithName(spamWordRegistry: {
        name: string;
    }): Promise<SpamWordRegistry>;
    getSpamWords(): Promise<SpamWordRegistry[]>;
    saveSpamWordRegistry(spamWordRegistry: SpamWordRegistry): Promise<SpamWordRegistry>;
    deleteSpamWordRegistry(id: string | number): Promise<any>;
};
