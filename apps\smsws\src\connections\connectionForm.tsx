import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  FormControlLabel,
  MenuItem,
  Paper,
  Switch,
} from "@mui/material";
import { CommandCell, InputCell } from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscApplicationsService } from "@pnmui/common/services/smsc/applicationsService";
import { SmscConnectionsService } from "@pnmui/common/services/smsc/connectionsService";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import { SmscIpListsService } from "@pnmui/common/services/smsc/ipListsService";
import { Grid, GridToolbar, Column } from "../components/KendoGridWrapper";
import { ChangeEvent, useEffect, useState } from "react";
import {
  SelectValidator,
  TextValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { tap } from "rxjs/operators";
import "../common.css";
import { Application } from "../types";

interface TrafficActivityMonitoring {
  monitoringType: string;
  enabled: boolean;
  enquireLinkMessageExchange: boolean;
  monitoringWindows: number;
  windowSeconds: number;
}

interface AdvancedProperty {
  id: string | number;
  propertyName?: string;
  propertyValue?: string;
  name?: string;
  value?: string;
  inEdit?: boolean;
  section?: string | null;
  createdTime?: Date | null;
  lastUpdatedTime?: Date | null;
}

interface Connection {
  id?: string | number;
  name: string;
  applicationSelection: string;
  application: { id?: string | number; name: string };
  password: string;
  systemId: string;
  maxBinds: number;
  ipAllowlistEnabled: boolean;
  ipAllowlist: { id?: string | number; name: string } | null;
  esmLinkIntegrityMonitoring: boolean;
  trafficActivityMonitoring: TrafficActivityMonitoring;
  advancedProperties: AdvancedProperty[];
}

interface EnumerationOption {
  value: string;
  displayText: string;
}

interface Enumerations {
  applicationSelectionStrategy?: EnumerationOption[];
  connectionMonitoringStrategy?: EnumerationOption[];
  [key: string]: EnumerationOption[] | undefined;
}

interface PropertyErrors {
  propertyName?: string;
  propertyValue?: string;
}

interface FormErrors {
  advancedProperties?: {
    [key: string]: PropertyErrors;
  };
}

const SmscConnectionForm = () => {
  const navigate = useNavigate();
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const params = useParams<{ id: string }>();
  const [connection, setConnection] = useState<Connection>({
    id: 0,
    name: "",
    applicationSelection: "",
    application: { name: "" },
    password: "",
    systemId: "",
    maxBinds: 0,
    ipAllowlistEnabled: false,
    ipAllowlist: null,
    esmLinkIntegrityMonitoring: false,
    trafficActivityMonitoring: {
      monitoringType: "",
      enabled: false,
      enquireLinkMessageExchange: false,
      monitoringWindows: 0,
      windowSeconds: 0,
    },
    advancedProperties: [],
  });
  const [enumerations, setEnumerations] = useState<Enumerations>({});
  const [applications, setApplications] = useState<Application[]>([]);
  const [ipLists, setIpLists] = useState<Application[]>([]);
  const [applicationInputValue, setApplicationInputValue] = useState<string>("");
  const [ipListInputValue, setIpListInputValue] = useState<string>("");
  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });

    SmscEnumerationsService.getEnumerations().then(setEnumerations);

    const fetchData = async () => {
      const [apps, ips, connectionData] = await Promise.all([
        SmscApplicationsService.getApplications(),
        SmscIpListsService.getIpLists(),
        params["id"] && params["id"] !== "new"
          ? SmscConnectionsService.getConnectionById(params["id"])
          : Promise.resolve(null),
      ]);

      setApplications(apps);
      setIpLists(ips);

      if (connectionData) {
        const mappedApplication =
          apps.find((app) => app.id === connectionData.application) || null;
        const mappedIpAllowlist =
          ips.find((ip) => ip.id === connectionData.ipAllowlist) || null;

        setConnection({
          ...mapFromWebserviceObject(connectionData),
          application: mappedApplication,
          ipAllowlist: mappedIpAllowlist,
        });
      }
    };

    fetchData();
  }, [params]);

  const handleChange = (e: ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    if (!e.target.name) return;

    if (e.target.name.indexOf(".") > -1) {
      const [parent, child] = e.target.name.split(".");
      setConnection({
        ...connection,
        [parent]: {
          ...connection[parent as keyof Connection],
          [child]: e.target.value,
        },
      });
    } else {
      setConnection({
        ...connection,
        [e.target.name]: e.target.value,
      });
    }
  };

  const handleSubmit = (): void => {
    let hasValidationErrors = false;
    const newErrors: FormErrors = { advancedProperties: {} };

    // Validate advanced properties
    if (connection.advancedProperties?.length > 0) {
      connection.advancedProperties.forEach(property => {
        const propertyErrors = validateAdvancedProperty(property);
        if (Object.keys(propertyErrors).length > 0) {
          if (newErrors.advancedProperties) {
            newErrors.advancedProperties[property.id] = propertyErrors;
          }
          hasValidationErrors = true;
        }
      });
    }

    if (hasValidationErrors) {
      setErrors(newErrors);
      toast.error("Please fix validation errors before submitting");
      return;
    }

    SmscConnectionsService.saveConnection(mapToWebserviceObject(connection))
      .then((data) => {
        console.log("handleSubmit data", data);
        toast.success("Connection saved successfully");
        navigate("/smscConnections");
      })
      .catch((error: any) => {
        console.error("handleSubmit error", error);
        toast.error("Failed to save connection: " + error.message);
      });
  };

  function goBack(): void {
    navigate(-1);
  }

  const addNew = (): void => {
    const newDataItem: AdvancedProperty = {
      inEdit: true,
      id: Date.now(),
      propertyName: '',
      propertyValue: ''
    };
    setConnection({
      ...connection,
      advancedProperties: [newDataItem, ...connection.advancedProperties],
    });
  };

  function onItemChange(event: any): void {
    const { dataItem, field, value } = event;
    const foundPosition = connection.advancedProperties.findIndex(
      (s) => s.id === dataItem.id
    );
    const position = foundPosition === -1 ? 0 : foundPosition;
    const advancedProps = [...connection.advancedProperties];
    advancedProps.splice(position, 1, {
      ...advancedProps[position],
      [field]: value,
      inEdit: true
    });
    setConnection({
      ...connection,
      advancedProperties: advancedProps,
    });
  }


  function mapFromWebserviceObject(webserviceObject: any): Connection {
    return {
      id: webserviceObject.id || 0,
      name: webserviceObject.name || "",
      applicationSelection: webserviceObject.applicationSelectionStrategy || "",
      application: webserviceObject.application || { name: "" },
      password: webserviceObject.password || "",
      systemId: webserviceObject.systemId || "",
      maxBinds: webserviceObject.maxBinds || 0,
      ipAllowlistEnabled: webserviceObject.ipAllowlistEnabled || false,
      ipAllowlist: webserviceObject.ipAllowlist || null,
      esmLinkIntegrityMonitoring:
        webserviceObject.connectionMonitoringStrategy ===
        "ENQUIRE_LINK_MESSAGE_EXCHANGE",
      trafficActivityMonitoring: {
        monitoringType: webserviceObject.connectionMonitoringStrategy || "",
        enabled:
          webserviceObject.connectionMonitoringStrategy ===
          "TRAFFIC_ACTIVITY_MONITORING",
        enquireLinkMessageExchange:
          webserviceObject.connectionMonitoringStrategy ===
          "ENQUIRE_LINK_MESSAGE_EXCHANGE",
        monitoringWindows:
          webserviceObject.trafficActivityMonitoringErrorThreshold || 0,
        windowSeconds:
          webserviceObject.trafficActivityMonitoringWindowSeconds || 0,
      },
      advancedProperties: (webserviceObject.additionalProperties || []).map((prop: any) => ({
        ...prop,
        propertyName: prop.name || "",
        propertyValue: prop.value || "",
      })),
    };
  }

  function mapToWebserviceObject(connection: Connection): any {
    return {
      id: connection.id || 0,
      name: connection.name || "",
      applicationSelectionStrategy: connection.applicationSelection || "",
      application: connection.application?.id
        ? connection.application.id
        : null,
      password: connection.password || "",
      systemId: connection.systemId || "",
      maxBinds: connection.maxBinds || 0,
      ipAllowlistEnabled: connection.ipAllowlistEnabled || false,
      ipAllowlist: connection.ipAllowlist
        ? connection.ipAllowlist.id || connection.ipAllowlist
        : null,
      connectionMonitoringStrategy:
        connection.trafficActivityMonitoring?.monitoringType ||
        "TRAFFIC_ACTIVITY_MONITORING",
      trafficActivityMonitoringErrorThreshold:
        connection.trafficActivityMonitoring?.monitoringWindows || 0,
      trafficActivityMonitoringWindowSeconds:
        connection.trafficActivityMonitoring?.windowSeconds || 0,
      additionalProperties: (connection.advancedProperties || []).map(prop => ({
        ...prop,
        name: prop.propertyName,
        value: prop.propertyValue,
      })),
    };
  }

  function addNewApplication(): void {
    const newApplication = {
      name: applicationInputValue,
    };
    SmscApplicationsService.createApplicationWithName(newApplication).then(
      (data: Application) => {
        setApplications([...applications, data]);
        setConnection({
          ...connection,
          application: { id: data.id, name: data.name! },
        });
      }
    );
  }

  function addNewIpList(): void {
    const newIpList = {
      name: ipListInputValue,
    };
    SmscIpListsService.createIpListWithName(newIpList).then((data: Application) => {
      setIpLists([...ipLists, data]);
      setConnection({
        ...connection,
        ipAllowlist: { id: data.id, name: data.name! },
      });
    });
  }

  const getErrorMessage = (propertyId: string | number, field: string): string | undefined => {
    return errors?.advancedProperties?.[propertyId]?.[field as keyof PropertyErrors];
  };

  const validateAdvancedProperty = (property: AdvancedProperty): PropertyErrors => {
    const errors: PropertyErrors = {};

    if (!property.propertyName?.trim()) {
      errors.propertyName = 'Property name is required';
    }

    if (!property.propertyValue?.trim()) {
      errors.propertyValue = 'Property value is required';
    }

    return errors;
  };


  return (
    <div style={{ padding: "0.5em" }}>
      <ValidatorForm onSubmit={handleSubmit} className="tango-form">
        <div style={{ marginLeft: "1em" }}>
          <Card>
            <CardContent>
              <TextValidator
                label={i18n["smsc.connectionForm.list.name"] || "Name"}
                onChange={handleChange}
                name="name"
                value={connection.name}
                validators={["required"]}
                errorMessages={i18n["spcm.name.required"] || "Name is required"}
              />

              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  flexWrap: "wrap",
                }}
              >
                <TextValidator
                  label={i18n["smsc.SystemID.list.name"] || "System ID"}
                  onChange={handleChange}
                  name="systemId"
                  value={connection.systemId}
                  validators={["required"]}
                  errorMessages={
                    i18n["spcm.systemId.required"] || "System ID is required"
                  }
                />
                <TextValidator
                  label={i18n["ssrv.dbconnection.password"] || "Password"}
                  onChange={handleChange}
                  name="password"
                  value={connection.password}
                  type="password"
                  validators={["required"]}
                  errorMessages={
                    i18n["smsc.Password.required.name"] ||
                    "Password is required"
                  }
                />
                <TextValidator
                  type={"number"}
                  label={i18n["smsc.maxBinds.name"] || "Max Binds"}
                  onChange={handleChange}
                  name="maxBinds"
                  value={connection.maxBinds}
                  validators={["required", "minNumber:1"]}
                  errorMessages={
                    (i18n["smsc.maxBinds.required"] || "Max Binds is required",
                    "Max Binds must be at least 1")
                  }
                />
              </div>

              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  flexWrap: "wrap",
                }}
              >
                <SelectValidator
                  label={
                    i18n["smsc.connectionForm.ApplicationSelection.label"] ||
                    "Application Selection"
                  }
                  onChange={(e) =>
                    setConnection({
                      ...connection,
                      applicationSelection: e.target.value,
                    })
                  }
                  name="applicationSelection"
                  value={connection.applicationSelection}
                  validators={["required"]}
                  errorMessages={
                    i18n["smsc.connectionForm.Application.required"] ||
                    "Application is required"
                  }
                >
                  {enumerations.applicationSelectionStrategy?.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.displayText}
                    </MenuItem>
                  ))}
                </SelectValidator>

                {connection.applicationSelection === "SPECIFIC_APPLICATION" && (
                  <Autocomplete
                    name={"application"}
                    options={applications}
                    getOptionLabel={(option) => option.name || ""}
                    value={connection.application || null}
                    onChange={(e, value) =>
                      setConnection({
                        ...connection,
                        application: value || { id: "", name: "" },
                      })
                    }
                    inputValue={applicationInputValue}
                    onInputChange={(e, value) => setApplicationInputValue(value)}
                    renderInput={(params) => (
                      <TextValidator
                        {...params}
                        label={"Application"}
                        placeholder={
                          i18n["smsc.connectionForm.Application.placeholder"] ||
                          "Type to select or add an Application"
                        }
                        value={applicationInputValue}
                        name="application"
                        validators={["required"]}
                        errorMessages={[
                          i18n["smsc.connectionForm.Application.required"] ||
                          "Application is required"
                        ]}
                      />
                    )}
                    PaperComponent={(e) => (
                      <Paper>
                        {e.children}
                        <Button
                          disabled={
                            !applicationInputValue ||
                            !!applications.find(
                              (a) => a.name === applicationInputValue
                            )
                          }
                          color="primary"
                          fullWidth
                          sx={{ justifyContent: "flex-start", pl: 2 }}
                          onMouseDown={(ev) => {
                            addNewApplication();
                          }}
                        >
                          {i18n["smsc.application.createWithName.addNew"] ||
                            "+ Add New"}
                        </Button>
                      </Paper>
                    )}
                  />
                )}
              </div>

              <div
                style={{
                  borderTop: "1px solid #ccc",
                  padding: "1em",
                  marginTop: "1em",
                }}
              >
                <FormControlLabel
                  control={
                    <Switch
                      checked={connection.ipAllowlistEnabled}
                      onChange={(e) =>
                        setConnection({
                          ...connection,
                          ipAllowlistEnabled: e.target.checked,
                        })
                      }
                      name="ipAllowlistEnabled"
                    />
                  }
                  label={
                    i18n["smsc.connectionForm.ipAllowList"] || "IP Allowlist"
                  }
                />

                {connection.ipAllowlistEnabled && (
                  <Autocomplete
                    options={ipLists}
                    getOptionLabel={(option) => option.name || ""}
                    value={connection.ipAllowlist || null}
                    onChange={(e, value) =>
                      setConnection({
                        ...connection,
                        ipAllowlist: value || { id: "", name: "" },
                      })
                    }
                    inputValue={ipListInputValue}
                    onInputChange={(e, value) => setIpListInputValue(value)}
                    style={{ width: "20em" }}
                    renderInput={(params) => (
                      <TextValidator
                        {...params}
                        label={i18n["smsc.connectionForm.ipLists"] || "IP Lists"}
                        placeholder="Type to select or add an IP Allowlist"
                        value={ipListInputValue}
                        name="ipAllowlist"
                        validators={["required"]}
                        errorMessages={[
                          i18n["smsc.connectionForm.ipList.required"] ||
                          "IP List is required"
                        ]}
                      />
                    )}
                    PaperComponent={(e) => (
                      <Paper>
                        {e.children}
                        <Button
                          disabled={
                            !ipListInputValue ||
                            !!ipLists.find((a) => a.name === ipListInputValue)
                          }
                          color="primary"
                          fullWidth
                          sx={{ justifyContent: "flex-start", pl: 2 }}
                          onMouseDown={(ev) => {
                            addNewIpList();
                          }}
                        >
                          {i18n["smsc.application.createWithName.addNew"] ||
                            "+ Add New"}
                        </Button>
                      </Paper>
                    )}
                  />
                )}
              </div>

              <Accordion
                style={{
                  marginTop: "1em",
                  borderTop: "none",
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  backgroundColor: "#f9f9f9",
                  boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <span className={"accordion-title"}>
                    {" "}
                    {
                      i18n[
                        "hrg.connections.connectionList.monitoring" ||
                          "Monitoring"
                      ]
                    }
                  </span>
                </AccordionSummary>
                <AccordionDetails
                  className="flex-container"
                  style={{ justifyContent: "start" }}
                >
                  <div className="monitoring-content">
                    <SelectValidator
                      label={
                        i18n["smsc.connectionForm.integrity"] ||
                        "Mechanism to monitor ESME link integrity"
                      }
                      onChange={handleChange}
                      name="trafficActivityMonitoring.monitoringType"
                      style={{
                        width: "25em",
                      }}
                      value={
                        connection.trafficActivityMonitoring?.monitoringType
                      }
                    >
                      {enumerations.connectionMonitoringStrategy?.map(
                        (option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.displayText}
                          </MenuItem>
                        )
                      )}
                    </SelectValidator>

                    {connection.trafficActivityMonitoring?.monitoringType ===
                      "TRAFFIC_ACTIVITY_MONITORING" && (
                      <div className="traffic-activity-monitoring">
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <span style={{ color: "red" }}>If </span>&nbsp;&nbsp;
                          <span>
                            {" "}
                            {
                              i18n[
                                "smsc.connectionForm.accordion.noMessages" ||
                                  "NO SMPP messages from the ESME are detected in"
                              ]
                            }
                            &nbsp;
                          </span>
                          <TextValidator
                            type={"number"}
                            onChange={handleChange}
                            name="trafficActivityMonitoring.monitoringWindows"
                            value={
                              connection.trafficActivityMonitoring
                                ?.monitoringWindows || 0
                            }
                            validators={["required", "minNumber:1"]}
                            errorMessages={[
                              "Monitoring Windows is required",
                              "Monitoring Windows must be at least 1",
                            ]}
                          />
                          {i18n["smsc.connectionForm.trafficMonitoring"] ||
                            "Traffic Monitoring Windows, each Window being"}
                          <TextValidator
                            type={"number"}
                            onChange={handleChange}
                            name="trafficActivityMonitoring.windowSeconds"
                            value={
                              connection.trafficActivityMonitoring
                                ?.windowSeconds || 0
                            }
                            validators={["required", "minNumber:1"]}
                            errorMessages={[
                              "Window Seconds is required",
                              "Window Seconds must be at least 1",
                            ]}
                            style={{ width: "50px", margin: "0 5px" }}
                          />
                          {
                            i18n[
                              "smsws.analysisProfile.default.window.label.2" ||
                                "seconds"
                            ]
                          }
                        </div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginTop: "2em",
                          }}
                        >
                          <span style={{ color: "red" }}>Then</span>
                          &nbsp;
                          <span>
                            {i18n["smsc.connectionForm.accordion.inactive"] ||
                              "link is inactive and the connection to the ESME"}
                            closes
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </AccordionDetails>
              </Accordion>

              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <span className={"accordion-title"}>
                    {i18n["smsc.applicationForm.accordion.title"] || "Advanced"}
                  </span>
                </AccordionSummary>
                <AccordionDetails className="flex-container">
                  <div
                    style={{
                      width: "100%",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                    }}
                  >
                    <p>
                      {i18n[
                        "smsc.applicationForm.propertyvalue.advanced.properties"
                      ] ||
                        "Here you can configure additional properties. Refer to Admin Guide for full list"}
                    </p>
                    <Grid
                      editField="inEdit"
                      editable={true}
                      onItemChange={onItemChange}
                      style={{
                        maxWidth: "60em",
                      }}
                      data={connection.advancedProperties || []}
                    >
                      <GridToolbar>
                        <button
                          type={"button"}
                          className="k-primary k-button k-grid-edit-command"
                          style={{ position: "absolute", right: "1em" }}
                          onClick={addNew}
                        >
                          {i18n["button.add"] || "Add"}
                        </button>
                      </GridToolbar>
                      <Column
                        field="propertyName"
                        title={i18n["spcm.PropertyName.label"] || "Property Name"}
                        cell={(props) => (
                          <InputCell
                            {...props}
                            onChange={onItemChange}
                            error={getErrorMessage(props.dataItem.id, 'propertyName')}
                            debounceDelay={500}
                          />
                        )}
                      />
                      <Column
                        field="propertyValue"
                        title={i18n["spcm.PropertyValue.label"] || "Property Value"}
                        cell={(props) => (
                          <InputCell
                            {...props}
                            onChange={onItemChange}
                            error={getErrorMessage(props.dataItem.id, 'propertyValue')}
                            debounceDelay={500}
                          />
                        )}
                      />
                      <Column
                        cell={(props) => (
                          <CommandCell
                            {...props}
                            item={connection}
                            onChange={(data) => {
                              setConnection({
                                ...connection,
                                advancedProperties: data,
                              });
                            }}
                            onSave={(dataItem) => {
                              const validationErrors = validateAdvancedProperty(dataItem);
                              if (Object.keys(validationErrors).length > 0) {
                                setErrors((prev) => ({
                                  ...prev,
                                  advancedProperties: {
                                    ...prev?.advancedProperties,
                                    [dataItem.id]: validationErrors
                                  }
                                }));
                                toast.error(Object.values(validationErrors)[0]);
                                return false;
                              }
                              return true;
                            }}
                            gridProp={"advancedProperties"}
                          />
                        )}
                        filterable={false}
                      />
                    </Grid>
                  </div>
                </AccordionDetails>
              </Accordion>
            </CardContent>
            <CardActions className="card-actions content-card-actions">
              <Button
                variant="contained"
                color="secondary"
                type="button"
                onClick={goBack.bind(this)}
              >
                {i18n["button.back"] || "Back"}
              </Button>
              {SecurityService.checkPermission(
                "SMSC_CONNECTION_UPDATE_PERMISSION"
              ) && (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  className="request-handler-submit-button"
                >
                  {i18n["button.submit"] || "Save"}
                </Button>
              )}
            </CardActions>
          </Card>
        </div>
      </ValidatorForm>
    </div>
  );
};

export default SmscConnectionForm;





