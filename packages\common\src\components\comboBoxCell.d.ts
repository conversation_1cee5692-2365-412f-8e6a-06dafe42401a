import React from "react";
interface Option {
    name: string;
    inputValue?: string;
    [key: string]: any;
}
interface DataItem {
    [key: string]: any;
    inEdit?: boolean;
}
interface ChangeEvent {
    dataIndex: number;
    dataItem: DataItem;
    field: string;
    syntheticEvent: React.SyntheticEvent;
    value: any;
}
interface ComboBoxCellProps {
    dataItem: DataItem;
    field?: string;
    options: Option[];
    onChange?: (event: ChangeEvent) => void;
    onAddNew?: (value: string) => void;
    error?: string;
}
declare const ComboBoxCell: React.FC<ComboBoxCellProps>;
export default ComboBoxCell;
