import React, { useState } from "react";
import BlackoutPeriod from "../smsc/smppApplicationProfiles/blackoutPeriod";

interface Weekday {
  value?: number;
}

interface DataItem {
  inEdit?: boolean;
  start?: string;
  end?: string;
  startDate?: string;
  endDate?: string;
  weekdays?: Array<Weekday | number>;
  [key: string]: any;
}

interface DurationCellProps {
  dataItem: DataItem;
  dataIndex?: number;
  onUpdate: (dataItem: DataItem, dataIndex?: number) => void;
}

const DurationCell: React.FC<DurationCellProps> = (props) => {
  const [blackoutPeriodOpen, setBlackoutPeriodOpen] = useState<boolean>(false);
  const { dataItem } = props;

  const weekdays: string[] = [
    "Sundays",
    "Mondays",
    "Tuesdays",
    "Wednesdays",
    "Thursdays",
    "Fridays",
    "Saturdays",
  ];

  const handleItemChange = (value: Partial<DataItem>): void => {
    const newDataItem = { ...dataItem, ...value };
    props.onUpdate(newDataItem, props.dataIndex);
  };

  function formatPeriodPeriod(period?: string): string {
    if (!period) return '';
    
    return period.indexOf("T") !== -1
      ? `${period.split("T")[0].substring(0, 10)} ${period.split("T")[1].substring(0, 5)}`
      : period;
  }

  const renderPeriodInfo = (): JSX.Element => {
    if (dataItem.weekdays && dataItem.weekdays.length) {
      return (
        <>
          <span>
            <span style={{ fontWeight: "bold" }}>Start</span>:{" "}
            {formatPeriodPeriod(dataItem.start)}
          </span>
          <span>
            <span style={{ fontWeight: "bold" }}>End</span>:{" "}
            {formatPeriodPeriod(dataItem.end)}
          </span>
          <span>
            <span style={{ fontWeight: "bold" }}>On</span>:{" "}
            {dataItem.weekdays.map((w, it) => {
              const weekdayIndex = typeof w === 'object' && 'value' in w && w.value === 0 
                ? 0 
                : (typeof w === 'object' && 'value' in w ? w.value : w);
              
              if (typeof weekdayIndex === 'number' && weekdayIndex >= 0 && weekdayIndex < weekdays.length) {
                return `${weekdays[weekdayIndex]}${it < (dataItem.weekdays?.length || 0) - 1 ? ", " : ""}`;
              }
              return '';
            })}
          </span>
        </>
      );
    }

    return (
      <>
        <span>
          <span style={{ fontWeight: "bold" }}>Start</span>:{" "}
          {`${formatPeriodPeriod(dataItem.startDate)} ${formatPeriodPeriod(dataItem.start)}`}
        </span>
        <span>
          <span style={{ fontWeight: "bold" }}>End</span>:{" "}
          {`${formatPeriodPeriod(dataItem.endDate)} ${formatPeriodPeriod(dataItem.end)}`}
        </span>
      </>
    );
  };

  return (
    <td>
      {dataItem.inEdit ? (
        <>
          <BlackoutPeriod
            data={dataItem}
            open={blackoutPeriodOpen}
            onClose={(value) => {
              handleItemChange(value);
              setBlackoutPeriodOpen(false);
            }}
          />
          <button
            type="button"
            className="k-primary k-button k-grid-edit-command"
            onClick={() => setBlackoutPeriodOpen(true)}
          >
            Edit Period
          </button>
        </>
      ) : (
        <div style={{ display: "flex", flexDirection: "column" }}>
          {renderPeriodInfo()}
        </div>
      )}
    </td>
  );
};

export default DurationCell;
