import { $i18n } from "@pnmui/common/services/propertiesService";
import { FC, useEffect, useRef, useState } from "react";

interface ErrorResponseData {
  errors?: Record<string, string>;
  data?: any;
  statusText?: string;
  message?: string;
}

interface ErrorResponse {
  response?: ErrorResponseData;
  message?: string;
}

interface ErrorsDisplayProps {
  errorResponse: ErrorResponse | null;
  keyPrefix: string;
}

const ErrorsDisplay: FC<ErrorsDisplayProps> = ({
  errorResponse,
  keyPrefix,
}) => {
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [errors, setErrors] = useState<string[]>([]);
  const errorsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const subscription = $i18n.subscribe(
      (i18NProps: Record<string, string>) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  useEffect(() => {
    if (errorResponse) {
      console.error("Error Response:", errorResponse);
      if (errorResponse.response?.errors) {
        let errs = Object.keys(errorResponse.response.errors).map((key) => {
          return `${i18n[keyPrefix + "." + key] || key}: ${
            i18n[errorResponse.response?.errors?.[key] || ""] ||
            errorResponse.response?.errors?.[key]
          }`;
        });
        setErrors(errs);
      } else {
        setErrors([
          errorResponse.response?.data ||
            errorResponse.response?.statusText ||
            errorResponse.message ||
            (errorResponse.response as any)?.message ||
            "Unknown error",
        ]);
      }
    }
  }, [errorResponse, i18n, keyPrefix]);

  useEffect(() => {
    if (errorsRef.current) {
      errorsRef.current.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }, [errors]);

  return errors.length ? (
    <div
      className={"errors-container"}
      style={{ marginBottom: "2em", marginTop: "1em" }}
      ref={errorsRef}
    >
      {errors.map((error, index) => {
        return (
          <div key={"error_" + index} className={"error"}>
            - {error}
          </div>
        );
      })}
    </div>
  ) : null;
};

export default ErrorsDisplay;
