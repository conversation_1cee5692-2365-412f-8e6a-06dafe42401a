import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  MenuItem,
  Typography,
} from "@mui/material";
import React, { ChangeEvent } from "react";
import { SelectValidator } from "react-material-ui-form-validator";
import { handleChange } from "./common";
import { ApplicationProfile, EnumerationOption } from "../types";

interface EncodingSettings {
  defaultAlphabetForReceivedSMS?: string;
  defaultAlphabetForDeliveryOfSMS?: string;
}

interface EncodingAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  enumerations: Record<string, EnumerationOption[]>;
}

const EncodingAccordion: React.FC<EncodingAccordionProps> = ({
  applicationProfile,
  setApplicationProfile,
  enumerations,
}) => {
  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      defaultExpanded={true}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography className="accordion-title">Encoding</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{ justifyContent: "start" }}
      >
        <div className="flex-container" style={{ justifyContent: "start" }}>
          <SelectValidator
            label="Default Alphabet for received SMS"
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange(e, setApplicationProfile)}
            name="encoding.defaultAlphabetForReceivedSMS"
            value={applicationProfile.encoding?.defaultAlphabetForReceivedSMS || "NONE"}
            style={{ minWidth: "250px", marginRight: "1em" }}
          >
            <MenuItem value="NONE">None</MenuItem>
            <MenuItem value="GSM_7BIT_UNPACKED_ESCAPED">GSM 7bit unpacked escaped</MenuItem>
            <MenuItem value="ISO_LATIN_15">ISO Latin 15</MenuItem>
          </SelectValidator>
          <SelectValidator
            label="Default Alphabet for delivery of SMS"
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange(e, setApplicationProfile)}
            name="encoding.defaultAlphabetForDeliveryOfSMS"
            value={applicationProfile.encoding?.defaultAlphabetForDeliveryOfSMS || "NONE"}
            style={{ minWidth: "250px" }}
          >
            <MenuItem value="NONE">None</MenuItem>
            <MenuItem value="GSM_7BIT_UNPACKED_ESCAPED">GSM 7bit unpacked escaped</MenuItem>
            <MenuItem value="ISO_LATIN_15">ISO Latin 15</MenuItem>
          </SelectValidator>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default EncodingAccordion;
