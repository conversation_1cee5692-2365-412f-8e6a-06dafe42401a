import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState, MouseEvent, ChangeEvent } from "react";
import { handleChange } from "./common";
import { ApplicationProfile } from "../types";

interface ThrottlingSettings {
  enabled?: boolean;
  maxMessagesPerSecond?: number;
  maxMessagesPerMinute?: number;
  maxMessagesPerHour?: number;
  maxMessagesPerDay?: number;
}

interface ThrottlingAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  errors: Record<string, any>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

const ThrottlingAccordion: React.FC<ThrottlingAccordionProps> = ({ applicationProfile, setApplicationProfile, errors, setErrors }) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  useEffect(() => {
    setExpanded(applicationProfile.throttling?.enabled || false);
  }, [applicationProfile.throttling?.enabled]);

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.throttling?.enabled;
    setApplicationProfile((prev) => ({
      ...prev,
      throttling: {
        ...prev.throttling,
        enabled: newEnabled,
      },
    }));
    setExpanded(newEnabled);

    // Clear errors when disabled
    if (!newEnabled && setErrors) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.throttling;
        return newErrors;
      });
    }
  };

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{
              color: applicationProfile.throttling?.enabled ? "#3f51b5" : "gray",
              fontSize: "3em",
            }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable Throttling</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{
          justifyContent: "start",
          flexDirection: "column",
          width: "100%",
        }}
      >
        <div style={{ display: "flex", flexDirection: "row", alignItems: "center", flexFlow: "wrap", marginTop: "1em" }}>
          <span style={{ color: "red", marginRight: "0.3em" }}>If </span>
          <span> more than </span>
          <TextField
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange(e, setApplicationProfile)}
            size="small"
            name="throttling.maxMessages"
            type="number"
            value={applicationProfile.throttling?.maxMessages || 0}
            variant="outlined"
            className="small-input"
            error={Boolean(errors?.throttling?.maxMessages)}
            helperText={errors?.throttling?.maxMessages}
          />
          <span>SMS are attempted in </span>
          <TextField
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange(e, setApplicationProfile)}
            size="small"
            name="throttling.accuracy"
            type="number"
            value={applicationProfile.throttling?.accuracy || 0}
            variant="outlined"
            className="small-input"
            error={Boolean(errors?.throttling?.accuracy)}
            helperText={errors?.throttling?.accuracy}
          />
          <span> of the last </span>
          <TextField
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange(e, setApplicationProfile)}
            size="small"
            name="throttling.analysisWindow"
            type="number"
            value={applicationProfile.throttling?.analysisWindow || 0}
            variant="outlined"
            className="small-input"
            error={Boolean(errors?.throttling?.analysisWindow)}
            helperText={errors?.throttling?.analysisWindow}
          />
          <span> Analysis Windows, each Window being </span>
          <TextField
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange(e, setApplicationProfile)}
            size="small"
            name="throttling.timeWindow"
            type="number"
            value={applicationProfile.throttling?.timeWindow || 0}
            variant="outlined"
            className="small-input"
            error={Boolean(errors?.throttling?.timeWindow)}
            helperText={errors?.throttling?.timeWindow}
          />
          <span> seconds </span>
        </div>
        <p>
          <span style={{ color: "red" }}>Then</span> Throttle the Application
        </p>
      </AccordionDetails>
    </Accordion>
  );
};

export default ThrottlingAccordion;
