import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { ResourcePolicy } from "@pnmui/common/types/smscTypes";

export const SmscResourcePoliciesService = {
  getResourcePolicyById(id: string | number): Promise<ResourcePolicy> {
    return new Promise((resolve, reject) => {
      HttpService.get<ResourcePolicy>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/resourcePolicies/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  saveResourcePolicy(resourcePolicy: ResourcePolicy): Promise<ResourcePolicy> {
    return new Promise((resolve, reject) => {
      const isUpdate = !!resourcePolicy.id;
      const permission = isUpdate
        ? "SMSC_RESOURCE_POLICIES_UPDATE_PERMISSION"
        : "SMSC_RESOURCE_POLICIES_CREATE_PERMISSION";

      if (!SecurityService.checkPermission(permission)) {
        reject(
          `Permission Denied: You do not have access to ${
            isUpdate ? "update" : "create"
          } resource policies.`
        );
        return;
      }

      HttpService[isUpdate ? "put" : "post"]<ResourcePolicy>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/resourcePolicies${isUpdate ? "/" + resourcePolicy.id : ""}`,
        {},
        resourcePolicy
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  getResourcePolicies(): Promise<ResourcePolicy[]> {
    return new Promise((resolve, reject) => {
      HttpService.get<ResourcePolicy[]>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/resourcePolicies`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  deleteResourcePolicyById(id: string | number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (
        !SecurityService.checkPermission(
          "SMSC_RESOURCE_POLICIES_DELETE_PERMISSION"
        )
      ) {
        reject(
          "Permission Denied: You do not have access to delete resource policies."
        );
        return;
      }
      HttpService.delete(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/resourcePolicies/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  reloadResourcePolicies(host?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if (
        !SecurityService.checkPermission(
          "SMSC_RESOURCE_POLICIES_READ_PERMISSION"
        )
      ) {
        reject(
          "Permission Denied: You do not have access to reload resource policies."
        );
        return;
      }
      const baseUrl =
        $properties.value["client.smsws.base.url"] || "http://localhost:3500";

      let url = `${baseUrl}/resourcePolicies/reload`;
      if (host && host.trim() !== "") {
        const encodedHost = encodeURIComponent(host.trim());
        url += `?host=${encodedHost}`;
      }
      HttpService.get(url, {})
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch((error) => {
          console.error("Error in reloadResourcePolicies:", error);
          reject(error);
        });
    });
  },
};


