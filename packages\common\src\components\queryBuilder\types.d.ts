import { ReactNode } from 'react';
export interface QueryBuilderCommonProps {
    className?: string;
    style?: React.CSSProperties;
}
export interface RuleGroupProps extends QueryBuilderCommonProps {
    id?: string;
    path?: string[];
    field?: string;
    operator?: string;
    value?: any;
    rules?: RuleGroupProps[];
    combinator?: string;
    not?: boolean;
    schema?: any;
    translations?: any;
    context?: any;
    actions?: any;
    disabled?: boolean;
    parentDisabled?: boolean;
    independentCombinators?: boolean;
}
export interface RuleProps extends QueryBuilderCommonProps {
    id?: string;
    field?: string;
    operator?: string;
    value?: any;
    schema?: any;
    translations?: any;
    context?: any;
    actions?: any;
    disabled?: boolean;
    parentDisabled?: boolean;
}
export interface ValueSelectorProps extends QueryBuilderCommonProps {
    options?: Array<{
        name: string;
        label: string;
        [key: string]: any;
    }>;
    value?: string;
    title?: string;
    handleOnChange?: (value: string) => void;
    disabled?: boolean;
    multiple?: boolean;
}
export interface ActionProps extends QueryBuilderCommonProps {
    label?: string;
    title?: string;
    handleOnClick?: (e: React.MouseEvent) => void;
    disabled?: boolean;
    disabledTranslation?: string;
}
export interface ValueEditorProps extends QueryBuilderCommonProps {
    field?: string;
    operator?: string;
    value?: any;
    handleOnChange?: (value: any) => void;
    title?: string;
    type?: string;
    inputType?: string;
    values?: any[];
    disabled?: boolean;
    schema?: any;
    rule?: RuleProps;
}
export interface FieldSelectorProps extends QueryBuilderCommonProps {
    options?: Array<{
        name: string;
        label: string;
        [key: string]: any;
    }>;
    value?: string;
    title?: string;
    handleOnChange?: (value: string) => void;
    disabled?: boolean;
}
export interface OperatorSelectorProps extends QueryBuilderCommonProps {
    field?: string;
    fieldData?: any;
    options?: Array<{
        name: string;
        label: string;
        [key: string]: any;
    }>;
    value?: string;
    title?: string;
    handleOnChange?: (value: string) => void;
    disabled?: boolean;
}
export interface NotToggleProps extends QueryBuilderCommonProps {
    checked?: boolean;
    handleOnChange?: (checked: boolean) => void;
    title?: string;
    disabled?: boolean;
}
export interface CombinatorSelectorProps extends QueryBuilderCommonProps {
    options?: Array<{
        name: string;
        label: string;
        [key: string]: any;
    }>;
    value?: string;
    title?: string;
    handleOnChange?: (value: string) => void;
    disabled?: boolean;
    rules?: RuleGroupProps[];
}
export interface DragHandleProps extends QueryBuilderCommonProps {
    title?: string;
    label?: string;
    disabled?: boolean;
}
export interface ShiftActionsProps extends QueryBuilderCommonProps {
    level?: number;
    path?: string[];
    title?: string;
    disabled?: boolean;
}
export interface ComponentProps extends QueryBuilderCommonProps {
    type?: string;
    id?: string;
    path?: string[];
    field?: string;
    operator?: string;
    value?: any;
    rules?: RuleGroupProps[];
    combinator?: string;
    not?: boolean;
    schema?: any;
    translations?: any;
    context?: any;
    actions?: any;
    disabled?: boolean;
    parentDisabled?: boolean;
    independentCombinators?: boolean;
    children?: ReactNode;
    [key: string]: any;
}
