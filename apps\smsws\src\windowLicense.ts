// This file accesses the license key from the window object
import { setScriptKey } from '@progress/kendo-licensing';

// Access the license key from the window object
declare global {
  interface Window {
    KendoLicenseKey?: string;
  }
}

// Check if the license key is available in the window object
if (window.KendoLicenseKey) {
  // Set the license key
  setScriptKey(window.KendoLicenseKey);
  console.log('KendoReact license set successfully from window object');
} else {
  console.warn('KendoReact license key not found in window object');
}
