import { Component, ErrorInfo as ReactErrorInfo, ReactNode } from 'react';
import { WithChildren } from './types';
interface ErrorBoundaryProps extends WithChildren {
    fallback?: ReactNode;
}
interface ErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
    errorInfo: ReactErrorInfo | null;
}
declare class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps);
    static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState>;
    componentDidCatch(error: Error, errorInfo: ReactErrorInfo): void;
    render(): ReactNode;
}
export default ErrorBoundary;
