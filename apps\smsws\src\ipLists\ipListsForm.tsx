import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import {
  <PERSON><PERSON>,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  IconButton,
  List,
  ListItem,
  ListItemSecondaryAction,
} from "@mui/material";
import { ErrorsDisplay } from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscIpListsService } from "@pnmui/common/services/smsc/ipListsService";
import React, { ChangeEvent, useEffect, useState } from "react";
import { TextValidator, ValidatorForm } from "react-material-ui-form-validator";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";

interface IpListFormModel {
  id?: string | number;
  name: string;
  ipAddresses: string[];
  createdTime?: string;
  lastUpdatedTime?: string;
}

const SmscIpListForm = () => {
  const navigate = useNavigate();
  const params = useParams<{ id: string }>();
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [errorResponse, setErrorResponse] = useState<any>(null);
  const [ipList, setIpList] = useState<IpListFormModel>({
    name: "",
    ipAddresses: [],
  });
  const [newIpAddress, setNewIpAddress] = useState<string>("");

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });

    if (params.id && params.id !== "new") {
      SmscIpListsService.getIpListById(params.id)
        .then((data: IpListFormModel) => {
          setIpList(data);
        })
        .catch((error: any) => {
          setErrorResponse(error);
        });
    }

    return () => subscription.unsubscribe();
  }, [params.id]);

  const handleSubmit = (): void => {
    if (!ipList.name.trim()) {
      toast.error("Name is required");
      return;
    }

    if (ipList.ipAddresses.length === 0) {
      toast.error("At least one IP address is required");
      return;
    }

    SmscIpListsService.saveIpList(ipList)
      .then(() => {
        toast.success("IP List saved successfully");
        navigate("/smscIpLists");
      })
      .catch((error: any) => {
        setErrorResponse(error);
      });
  };

  const handleAddIp = (): void => {
    if (!newIpAddress) return;

    // Basic IP address validation
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipRegex.test(newIpAddress)) {
      toast.error("Please enter a valid IP address");
      return;
    }

    // Validate each octet
    const octets = newIpAddress.split(".");
    const validOctets = octets.every((octet) => {
      const num = parseInt(octet, 10);
      return num >= 0 && num <= 255;
    });

    if (!validOctets) {
      toast.error(
        "Please enter a valid IP address (octets must be between 0 and 255)"
      );
      return;
    }

    if (ipList.ipAddresses.includes(newIpAddress)) {
      toast.error("This IP address already exists in the list");
      return;
    }

    setIpList({
      ...ipList,
      ipAddresses: [...ipList.ipAddresses, newIpAddress],
    });
    setNewIpAddress("");
  };

  const handleRemoveIp = (ipToRemove: string): void => {
    setIpList({
      ...ipList,
      ipAddresses: ipList.ipAddresses.filter((ip) => ip !== ipToRemove),
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent): void => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddIp();
    }
  };

  const goBack = (): void => {
    navigate(-1);
  };

  return (
    <div style={{ padding: "0.5em" }}>
      <ValidatorForm onSubmit={handleSubmit} className="tango-form">
        <Card>
          <CardContent>
            <ErrorsDisplay
              errorResponse={errorResponse}
              keyPrefix={"ims.ipList.form.keys"}
            />

            <TextValidator
              label={i18n["ims.ipList.form.name"] || "Name"}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setIpList({ ...ipList, name: e.target.value })}
              name="name"
              value={ipList.name}
              validators={["required"]}
              errorMessages={["Name is required"]}
              fullWidth
              margin="normal"
            />

            <div style={{ marginTop: "2em" }}>
              <div
                style={{ display: "flex", gap: "1em", alignItems: "center" }}
              >
                <TextValidator
                  label={i18n["ims.ipList.form.ipAddress"] || "IP Address"}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setNewIpAddress(e.target.value)}
                  value={newIpAddress}
                  validators={["matchRegexp:^(\\d{1,3}\\.){3}\\d{1,3}$"]}
                  errorMessages={["Please enter a valid IP address"]}
                  style={{ flex: 1 }}
                  onKeyPress={handleKeyPress}
                />
                <IconButton
                  color="primary"
                  onClick={handleAddIp}
                  style={{ marginTop: "0.5em" }}
                >
                  <AddIcon />
                </IconButton>
              </div>

              <List
                style={{
                  marginTop: "1em",
                  maxHeight: "400px",
                  overflow: "auto",
                }}
              >
                {ipList.ipAddresses.map((ip, index) => (
                  <ListItem
                    key={index}
                    style={{
                      backgroundColor: "#f5f5f5",
                      marginBottom: "0.5em",
                      borderRadius: "4px",
                    }}
                  >
                    {ip}
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        aria-label="delete"
                        onClick={() => handleRemoveIp(ip)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </div>
          </CardContent>
          <CardActions className="card-actions content-card-actions">
            <Button
              variant="contained"
              color="secondary"
              type="button"
              onClick={goBack}
            >
              {i18n["button.cancel"] || "Cancel"}
            </Button>

            {SecurityService.checkPermission(
              "SMSC_IP_LISTS_UPDATE_PERMISSION"
            ) && (
              <Button variant="contained" color="primary" type="submit">
                {i18n["button.submit"] || "Submit"}
              </Button>
            )}
          </CardActions>
        </Card>
      </ValidatorForm>
    </div>
  );
};

export default SmscIpListForm;





