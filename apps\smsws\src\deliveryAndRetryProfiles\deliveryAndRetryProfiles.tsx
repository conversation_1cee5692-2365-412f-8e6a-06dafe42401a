import { <PERSON><PERSON>, <PERSON>, CardContent, <PERSON><PERSON>eader, MenuItem } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";

import { SmscDeliveryAndRetryProfilesService } from "@pnmui/common/services/smsc/deliveryAndRetryProfilesService";
import { SmscDeliveryAndRetrySchedulesService } from "@pnmui/common/services/smsc/deliveryAndRetrySchedulesService";
import { paginate } from "@pnmui/common/utils";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { DeliveryAndRetryProfile, DeliveryAndRetrySchedule } from "../types";

interface TableData {
  data: DeliveryAndRetryProfile[];
  page: number;
  totalCount: number;
}

function SmscDeliveryAndRetryProfiles() {
  const [originalProfiles, setOriginalProfiles] = useState<DeliveryAndRetryProfile[]>([]);
  const [profiles, setProfiles] = useState<DeliveryAndRetryProfile[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const navigate = useNavigate();
  const [retrySchedules, setRetrySchedules] = useState<DeliveryAndRetrySchedule[]>([]);

  const tableRefProfiles = React.createRef<any>();

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });
    populateProfiles();

    return () => subscription.unsubscribe();
  }, []);

  async function populateProfiles(): Promise<void> {
    try {
      const [profilesResponse, schedulesResponse] = await Promise.all([
        SmscDeliveryAndRetryProfilesService.getDeliveryAndRetryProfiles(),
        SmscDeliveryAndRetrySchedulesService.getDeliveryAndRetrySchedules(),
      ]);

      const profilesList: DeliveryAndRetryProfile[] = Array.isArray(profilesResponse)
        ? profilesResponse
        : [];
      const schedulesList: DeliveryAndRetrySchedule[] = Array.isArray(schedulesResponse)
        ? schedulesResponse
        : [];

      setProfiles(profilesList);
      setOriginalProfiles(profilesList);
      setRetrySchedules(schedulesList);
    } catch (error) {
      console.error("Error fetching profiles or schedules:", error);
    }
  }

  useEffect(() => {
    tableRefProfiles.current && tableRefProfiles.current.onQueryChange();
  }, [profiles]);

  async function populateProfilesData(query: { page: number; pageSize: number }): Promise<TableData> {
    const paginatedList = paginate(profiles, query.page, query.pageSize);
    return {
      data: paginatedList,
      page: query.page,
      totalCount: profiles.length,
    };
  }

  const handleRowSelection = (rows: DeliveryAndRetryProfile[]): void => {
    setSelectedRows(rows.map((row) => row.id || ''));
  };

  const handleDelete = async (): Promise<void> => {
    if (selectedRows.length === 0) {
      console.error("No rows selected for deletion.");
      return;
    }
    if (window.confirm("Are you sure you want to delete?")) {
      try {
        await Promise.all(
          selectedRows.map(async (id) => {
            return await SmscDeliveryAndRetryProfilesService.deleteRetryProfileById(
              id
            );
          })
        );
        const updatedProfiles = profiles.filter(
          (profile) => profile.id !== undefined && selectedRows.indexOf(profile.id) === -1
        );
        setProfiles([...updatedProfiles]);
        setSelectedRows([]);
        toast.success("Rows deleted successfully!");
      } catch (error: any) {
        console.error(error);
        toast.error(error.message);
      }
    }
  };

  useEffect(() => {
    tableRefProfiles.current && tableRefProfiles.current.onQueryChange();
  }, [profiles]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search(): void {
    const filteredProfiles = originalProfiles.filter((profile) =>
      profile.name?.toLowerCase().includes(selectedName.toLowerCase())
    );
    setProfiles(filteredProfiles);
  }

  return (
    <div className="wrapper">
      <Card className="content-card">
        <div className="form-row">
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>
              <ValidatorForm onSubmit={() => {}}>
                <SelectValidator
                  label={
                    i18n[
                      "smsc.deliveryAndRetryProfiles.retryProfileList.name"
                    ] || "Name"
                  }
                  margin="normal"
                  variant="outlined"
                  style={{
                    minWidth: "15em",
                    marginRight: "1rem",
                  }}
                  children={["Any", ...profiles.map((c) => c.name)].map(
                    (name) => (
                      <MenuItem key={name} value={name === "Any" ? "" : name}>
                        {name}
                      </MenuItem>
                    )
                  )}
                  onChange={(e) => setSelectedName(e.target.value)}
                  value={selectedName}
                />
              </ValidatorForm>
            </div>
          </div>
        </div>

        <CardContent>
          <MaterialTable
            tableRef={tableRefProfiles}
            icons={tableIcons}
            data={populateProfilesData}
            columns={[
              {
                field: "name",
                title:
                  i18n["smsc.deliveryAndRetryProfiles.retryProfileList.name"] ||
                  "Name",
              },
              {
                field: "delayBeforeFirstDelivery",
                title:
                  i18n["smsc.retryProfileForm.FirstDeliverylabel"] ||
                  "Delay before first delivery attempt in seconds",
                render: (rowData) =>
                  `${rowData.delayBeforeFirstDelivery} seconds`,
              },
              {
                field: "maxDeliveryAttempts",
                title:
                  i18n["smsc.retryProfileForm.MaxDeliveryAttemptsLabel"] ||
                  "Maximum delivery attempts",
              },

              {
                field: "maxExpiryTime",
                title:
                  i18n["smsc.retryProfileForm.MaxExpiryTimeLabel"] ||
                  "Maximum Expiry Time in minutes",
                render: (rowData) => `${rowData.maxExpiryTime} minutes`,
              },
              {
                title:
                  i18n["smsc.retryProfileForm.DefaultRetryScheduleLabel"] ||
                  "Default Retry Schedule",
                render: (rowData) => {
                  const schedule = retrySchedules.find(
                    (s) => s.id === rowData.defaultDeliveryAndRetrySchedule
                  );
                  return schedule ? schedule.name : "";
                },
              },
            ]}
            options={{
              selection: true,
              actionsColumnIndex: -1,
              toolbar: false,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              emptyRowsWhenPaging: false,
              headerStyle: { fontWeight: "bold" },
            }}
            onRowClick={(event, rowData) => {
              navigate(`/smscDeliveryAndRetryProfileForm/${rowData.id}`);
            }}
            onSelectionChange={(rows) => handleRowSelection(rows)}
          />
        </CardContent>

        <CardContent>
          <Button
            variant="contained"
            color="primary"
            className="request-handler-add-button"
            aria-label="Add"
            onClick={() => navigate("/smscDeliveryAndRetryProfileForm/new")}
            style={{
              marginLeft: "15px",
              marginTop: "2rem",
              marginBottom: "1rem",
            }}
            disabled={
              !SecurityService.checkPermission(
                "SMSC_DELIVERY_RETRY_PROFILES_CREATE_PERMISSION"
              )
            }
          >
            {i18n["button.add"] || "Add"}
          </Button>
          <span style={{ marginLeft: "10px" }}>
            <Button
              variant="contained"
              color="secondary"
              type="button"
              onClick={handleDelete}
              style={{ marginTop: "2rem", marginBottom: "1rem" }}
              className="request-handler-add-button"
              disabled={
                !SecurityService.checkPermission(
                  "SMSC_DELIVERY_RETRY_PROFILES_DELETE_PERMISSION"
                )
              }
            >
              {i18n["button.delete"] || "Delete"}
            </Button>
          </span>
        </CardContent>
      </Card>
    </div>
  );
}

export default SmscDeliveryAndRetryProfiles;





