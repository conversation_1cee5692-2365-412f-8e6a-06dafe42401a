import React from "react";
import { useTableKeyboardNavigation } from "@progress/kendo-react-data-tools";
import { $i18n } from "../services/propertiesService";

// Define the constant directly to avoid type issues
const GRID_COL_INDEX_ATTRIBUTE = 'data-grid-col-index';

interface DataItem {
  id?: string | number;
  tempId?: string | number;
  name?: string;
  externalId?: string;
  inEdit?: boolean;
  [key: string]: any;
}

interface ParentItem {
  [key: string]: DataItem[];
}

interface CommandCellProps {
  dataItem: DataItem;
  item: ParentItem;
  gridProp: string;
  onChange: (data: DataItem[]) => void;
  onSave?: (dataItem: DataItem) => boolean;
  onCancel?: () => void;
  id?: string;
  colSpan?: number;
  ariaColumnIndex?: number;
  isSelected?: boolean;
  columnIndex?: number;
  dataIndex?: number;
}

const CommandCell: React.FC<CommandCellProps> = (props) => {
  const { dataItem, item, gridProp, onChange } = props;
  const navigationAttributes = useTableKeyboardNavigation(props.id || '');
  const i18n = $i18n.value || {};

  // Basic null check
  if (!dataItem) {
    console.warn("CommandCell received null or undefined dataItem");
    return null;
  }

  const handleEdit = () => {
    const data = item[gridProp] || [];
    const updatedData = data.map((item) => ({
      ...item,
      inEdit: item.id === dataItem.id,
    }));
    onChange(updatedData);
  };

  const handleDelete = () => {
    const data = item[gridProp] || [];
    const updatedData = data.filter((item) => item.id !== dataItem.id);
    onChange(updatedData);
  };

  const handleSave = () => {
    if (props.onSave && !props.onSave(dataItem)) {
      return; // Validation failed
    }

    const data = item[gridProp] || [];
    const updatedData = data.map((item) => {
      if (item.id === dataItem.id) {
        return { ...item, inEdit: false };
      }
      return item;
    });
    onChange(updatedData);
  };

  const handleCancel = () => {
    const data = item[gridProp] || [];
    const originalItem = data.find((item) => item.id === dataItem.id);

    if (!originalItem) {
      // If it's a new item that was being added, remove it
      if (String(dataItem.id).includes('temp_')) {
        const updatedData = data.filter((item) => item.id !== dataItem.id);
        onChange(updatedData);

        // Call onCancel callback if provided
        if (props.onCancel) {
          props.onCancel();
        }
        return;
      }
    }

    // Otherwise, just turn off edit mode
    const updatedData = data.map((item) => {
      if (item.id === dataItem.id) {
        return { ...originalItem, inEdit: false };
      }
      return item;
    });

    onChange(updatedData);

    // Call onCancel callback if provided
    if (props.onCancel) {
      props.onCancel();
    }
  };

  return (
    <td
      className="k-command-cell"
      colSpan={props.colSpan}
      role={"gridcell"}
      aria-colindex={props.ariaColumnIndex}
      aria-selected={props.isSelected}
      {...{
        [GRID_COL_INDEX_ATTRIBUTE]: props.columnIndex,
      }}
      {...navigationAttributes}
    >
      {dataItem.inEdit ? (
        <>
          <button
            className="k-button k-grid-save-command"
            onClick={handleSave}
          >
            {i18n["button.save"] || "Save"}
          </button>
          <button
            className="k-button k-grid-cancel-command"
            onClick={handleCancel}
          >
            {i18n["button.cancel"] || "Cancel"}
          </button>
        </>
      ) : (
        <>
          <button
            className="k-button k-grid-edit-command"
            onClick={handleEdit}
          >
            {i18n["button.edit"] || "Edit"}
          </button>
          <button
            className="k-button k-grid-delete-command"
            onClick={handleDelete}
          >
            {i18n["button.delete"] || "Delete"}
          </button>
        </>
      )}
    </td>
  );
};

export default CommandCell;
