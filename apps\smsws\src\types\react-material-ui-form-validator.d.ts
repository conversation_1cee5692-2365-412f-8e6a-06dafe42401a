declare module 'react-material-ui-form-validator' {
  import * as React from 'react';

  export interface ValidatorFormProps {
    onSubmit?: (event: React.FormEvent<HTMLFormElement>) => void;
    onError?: (errors: any[]) => void;
    instantValidate?: boolean;
    children?: React.ReactNode;
    debounceTime?: number;
    [key: string]: any;
  }

  export interface TextValidatorProps {
    name: string;
    value: any;
    validators?: string[];
    errorMessages?: string[];
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    [key: string]: any;
  }

  export interface SelectValidatorProps {
    name: string;
    value: any;
    validators?: string[];
    errorMessages?: string[];
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    [key: string]: any;
  }

  export const ValidatorForm: React.ComponentType<ValidatorFormProps>;
  export const TextValidator: React.ComponentType<TextValidatorProps>;
  export const SelectValidator: React.ComponentType<SelectValidatorProps>;
}
