import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { ApplicationProfile } from "@pnmui/common/types/smscTypes";

export const SmscApplicationProfilesService = {
  getApplicationProfiles(): Promise<ApplicationProfile[]> {
    return new Promise((resolve, reject) => {
      // Check if we have a valid base URL before making the request
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn(`Cannot make application profiles request - missing base URL: ${baseUrl}`);
        reject(new Error('Missing or invalid base URL'));
        return;
      }

      HttpService.get<ApplicationProfile[]>(
        `${baseUrl}/applicationProfiles`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error('Error fetching application profiles:', error);
          reject(error);
        });
    });
  },

  getSmppApplicationProfileById(id: string | number): Promise<ApplicationProfile> {
    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot make application profile by ID request - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      HttpService.get<ApplicationProfile>(
        `${baseUrl}/applicationProfiles/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error(`Error fetching application profile with ID ${id}:`, error);
          reject(error);
        });
    });
  },

  saveSmppApplicationProfile(smppApplicationProfile: ApplicationProfile): Promise<ApplicationProfile | null> {
    const permission = smppApplicationProfile.id
      ? "SMSC_APPLICATION_PROFILE_UPDATE_PERMISSION"
      : "SMSC_APPLICATION_PROFILE_CREATE_PERMISSION";

    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot save application profile - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      if (SecurityService.checkPermission(permission)) {
        HttpService[smppApplicationProfile.id ? "put" : "post"]<ApplicationProfile>(
          `${baseUrl}/applicationProfiles${
            smppApplicationProfile.id ? "/" + smppApplicationProfile.id : ""
          }`,
          {},
          smppApplicationProfile
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(error => {
            console.error('Error saving application profile:', error);
            reject(error);
          });
      } else {
        console.error(`${permission} is required.`);
        resolve(null);
      }
    });
  },

  saveSmppApplicationProfileWithName(smppApplicationProfile: { name: string }): Promise<ApplicationProfile> {
    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot create application profile with name - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      HttpService.post<ApplicationProfile>(
        `${baseUrl}/applicationProfiles/createWithName`,
        {},
        smppApplicationProfile
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error('Error creating application profile with name:', error);
          reject(error);
        });
    });
  },

  deleteSmppApplicationProfileById(id: string | number): Promise<boolean | null> {
    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot delete application profile - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      if (
        SecurityService.checkPermission(
          "SMSC_APPLICATION_PROFILE_DELETE_PERMISSION"
        )
      ) {
        HttpService.delete(
          `${baseUrl}/applicationProfiles/${id}`,
          {}
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(error => {
            console.error(`Error deleting application profile with ID ${id}:`, error);
            reject(error);
          });
      } else {
        console.error(
          "SMSC_APPLICATION_PROFILE_DELETE_PERMISSION is required."
        );
        resolve(null);
      }
    });
  },
};


