import { Button as ButtonMui } from "./components/mui/Button";
import { DeleteButton as DeleteButtonMui } from "./components/mui/DeleteButton";
import { Component as ComponentMui } from "./components/mui/Component";
import { Input as InputMui } from "./components/mui/Input";
import { Select as SelectMui } from "./components/mui/Select";
import { SelectMulti as SelectMultiMui } from "./components/mui/SelectMulti";
import { Group as GroupMui } from "./components/mui/Group";
import { GroupHeaderOption as GroupHeaderOptionMui } from "./components/mui/GroupHeaderOption";
interface FormComponents {
    Input: typeof InputMui;
    Select: typeof SelectMui;
    SelectMulti: typeof SelectMultiMui;
}
interface MuiComponents {
    Add: typeof ButtonMui;
    Remove: typeof DeleteButtonMui;
    Component: typeof ComponentMui;
    Group: typeof GroupMui;
    GroupHeaderOption: typeof GroupHeaderOptionMui;
    form: FormComponents;
}
interface Components {
    mui: MuiComponents;
}
declare const components: Components;
export default components;
