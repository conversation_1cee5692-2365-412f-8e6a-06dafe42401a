/* eslint-disable react/jsx-no-bind */
import { CircularProgress, Modal } from "@mui/material";
import "./loading.css";
import React, { useState, FC } from "react";

const Loading: FC = () => {
  const [open] = useState<number>(0);

  function background(): React.ReactElement {
    return React.createElement("div", {
      className: "background",
    });
  }

  function handleClose(): void {}

  return (
    <Modal
      open={!!open}
      onClose={handleClose}
      className="loading"
      disableEnforceFocus
      disableAutoFocus
      BackdropComponent={background}
    >
      <CircularProgress color="primary" />
    </Modal>
  );
};

export default Loading;
