import { Grid<PERSON><PERSON><PERSON>, GridItemChangeEvent } from "@progress/kendo-react-grid";
import { Grid, Column } from "../components/KendoGridWrapper";
import React, { useState } from "react";
import { toast } from "react-toastify";
import { CommandCell, InputCell, SelectCell } from "@pnmui/common";
import { DeliveryAndRetrySchedule, EnumerationOption } from "../types";

interface StateException {
  id: string | number;
  stateToApplyException?: string | { id: string | number; name: string };
  mapErrorCode?: string | EnumerationOption;
  action?: string | EnumerationOption;
  delayAfterFailure?: number;
  nextState?: string | { id: string | number; name: string };
  inEdit?: boolean;
}

interface Enumerations {
  mapErrorCode?: EnumerationOption[];
  deliveryAndRetryScheduleStateAction?: EnumerationOption[];
  [key: string]: EnumerationOption[] | undefined;
}

interface StateExceptionErrors {
  stateToApplyException?: string;
  mapErrorCode?: string;
  action?: string;
  delayAfterFailure?: string;
  nextState?: string;
}

interface ErrorsState {
  [key: string]: StateExceptionErrors;
}

interface ScheduleExceptionsGridProps {
  allStateExceptions: StateException[];
  retrySchedule: DeliveryAndRetrySchedule;
  enumerations: Enumerations;
  onStateExceptionChange: (event: GridItemChangeEvent) => void;
  onAddNew: () => void;
  setAllStateExceptions: (exceptions: StateException[]) => void;
  setRetrySchedule: (schedule: DeliveryAndRetrySchedule) => void;
  validateStateException: (exception: StateException) => StateExceptionErrors;
  i18n: Record<string, string>;
}

export const ScheduleExceptionsGrid: React.FC<ScheduleExceptionsGridProps> = ({
  allStateExceptions,
  retrySchedule,
  enumerations,
  onStateExceptionChange,
  onAddNew,
  setAllStateExceptions,
  setRetrySchedule,
  validateStateException,
  i18n,
}) => {

  const [errors, setErrors] = useState<ErrorsState>({});

  // Custom display function to maintain consistent capitalization
  const displayActionText = (action: string | EnumerationOption | undefined): string => {
    if (!action) return "";
    const actionValue = typeof action === 'object' ? action.value : action;
    const enumAction = (enumerations.deliveryAndRetryScheduleStateAction || []).find(
      (a) => (a.value || a) === actionValue
    );
    return enumAction?.displayText || actionValue;
  };

  const getErrorMessage = (item: StateException | undefined, field: string): string | undefined => {
    return item?.id ? errors[item.id]?.[field as keyof StateExceptionErrors] : undefined;
  };

  // Handle action change to clear errors when action is STOP
  const handleActionChange = (event: GridItemChangeEvent): void => {
    const { dataItem, value } = event;
    const actionValue = typeof value === 'object' ? value?.value : value;

    // If action is changed to STOP, clear related validation errors
    if (actionValue === "STOP") {
      setErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        if (newErrors[dataItem.id]) {
          delete newErrors[dataItem.id].nextState;
          delete newErrors[dataItem.id].delayAfterFailure;

          // If no more errors for this item, remove the entry
          if (Object.keys(newErrors[dataItem.id]).length === 0) {
            delete newErrors[dataItem.id];
          }
        }
        return newErrors;
      });
    }

    // Call the original change handler
    onStateExceptionChange(event);
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "start",
      }}
    >
      <p>
        {i18n["smsc.scheduleExceptionsGrid.description"] ||
          "If you want to change the behavior when a specific MAP error is returned in a state, you can configure it here."}
      </p>
      <Grid
        editField="inEdit"
        editable={true}
        onItemChange={onStateExceptionChange}
        data={allStateExceptions}
        total={allStateExceptions?.length || 0}
      >
        <GridToolbar>
          <button
            type="button"
            className="k-primary k-button k-grid-edit-command"
            style={{ position: "absolute", right: "1em" }}
            onClick={onAddNew}
          >
            {i18n["smsc.scheduleExceptionsGrid.button.add"] || "Add"}
          </button>
        </GridToolbar>
        <Column
          field="stateToApplyException"
          title={
            i18n["smsc.scheduleExceptionsGrid.column.stateToApplyException"] ||
            "State to apply exception"
          }
          editable={true}
          cell={(props) => (
            <SelectCell
              {...props}
              options={
                retrySchedule.states && retrySchedule.states.length
                  ? retrySchedule.states
                  : []
              }
              value={props.dataItem}
              error={getErrorMessage(props.dataItem, 'stateToApplyException')}
            />
          )}
        />
        <Column
          field="mapErrorCode"
          title={
            i18n["smsc.scheduleExceptionsGrid.column.mapErrorReturned"] ||
            "If MAP Error returned is"
          }
          editable={true}
          cell={(props) => (
            <SelectCell
              {...props}
              options={enumerations.mapErrorCode || []}
              error={getErrorMessage(props.dataItem, 'mapErrorCode')}
            />
          )}
        />
        <Column
          field="action"
          title={i18n["smsc.scheduleExceptionsGrid.column.action"] || "Action"}
          editable={true}
          filterable={false}
          cell={(props) => (
            <SelectCell
              {...props}
              options={enumerations.deliveryAndRetryScheduleStateAction || []}
              value={props.dataItem}
              displayFunc={displayActionText}
              error={getErrorMessage(props.dataItem, 'action')}
              onChange={handleActionChange}
            />
          )}
        />
        <Column
          field="delayAfterFailure"
          title={
            i18n["smsc.scheduleExceptionsGrid.column.delayAfterRetry"] ||
            "Delay after Retry before calling next state(seconds)"
          }
          editable={true}
          cell={(props) => (
            <InputCell
              {...props}
              type="number"
              error={getErrorMessage(props.dataItem, 'delayAfterFailure')}
            />
          )}
        />
        <Column
          field="nextState"
          title={
            i18n["smsc.scheduleExceptionsGrid.column.nextState"] || "Next State"
          }
          editable={true}
          cell={(props) => (
            <SelectCell
              {...props}
              options={
                retrySchedule.states && retrySchedule.states.length
                  ? [...retrySchedule.states]
                  : []
              }
              value={props.dataItem}
              error={getErrorMessage(props.dataItem, 'nextState')}
            />
          )}
        />
        <Column
          cell={(props) => (
            <CommandCell
              {...props}
              item={{ stateExceptions: allStateExceptions }}
              onChange={(data: StateException[]) => {
                setAllStateExceptions(data);
                const updatedStates = [
                  ...retrySchedule.states.map((s) => ({
                    ...s,
                    stateExceptions: [],
                  })),
                ];
                for (const stateException of data) {
                  const foundState = updatedStates.find(
                    (st) =>
                      (stateException.stateToApplyException &&
                        st.id === stateException.stateToApplyException) ||
                      (stateException.stateToApplyException &&
                        typeof stateException.stateToApplyException === 'string' &&
                        st.name === stateException.stateToApplyException) ||
                      (typeof stateException.stateToApplyException === 'object' &&
                        stateException.stateToApplyException?.id &&
                        st.id === stateException.stateToApplyException?.id) ||
                      (typeof stateException.stateToApplyException === 'object' &&
                        stateException.stateToApplyException?.name &&
                        st.name === stateException.stateToApplyException?.name)
                  );
                  if (foundState) {
                    foundState.stateExceptions.push(stateException);
                  } else {
                    console.error(
                      "Used a State to Apply Exception that does not exist."
                    );
                  }
                }
                setRetrySchedule({ ...retrySchedule, states: updatedStates });
              }}
              onSave={(dataItem: StateException) => {
                console.log("dataItem", dataItem);
                const errorsResult = validateStateException(dataItem);
                console.log("errorsResult", errorsResult);

                // Don't show nextState and delayAfterFailure errors if action is STOP
                if (dataItem.action === "STOP" ||
                    (typeof dataItem.action === 'object' && dataItem.action?.value === "STOP")) {
                  delete errorsResult.nextState;
                  delete errorsResult.delayAfterFailure;
                }

                setErrors(prev => ({
                  ...prev,
                  [dataItem.id]: errorsResult
                }));

                if (Object.keys(errorsResult).length > 0) {
                  Object.values(errorsResult).forEach((error) => {
                    if (error) toast.error(error);
                  });
                  return false;
                }
                return true;
              }}
              gridProp="stateExceptions"
            />
          )}
          filterable={false}
        />
      </Grid>
    </div>
  );
};
