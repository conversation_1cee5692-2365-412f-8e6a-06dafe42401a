import { AxiosResponse } from "axios";
import { HttpRequestConfig, HttpResponse } from "./types";
interface Properties {
    subscribe: (observer: (data: Record<string, string>) => void) => {
        unsubscribe: () => void;
    };
}
export declare const HttpService: {
    init(properties?: Properties): void;
    getWithoutRouting(url: string): Promise<AxiosResponse>;
    get<T = any>(url: string, options?: HttpRequestConfig): Promise<HttpResponse<T>>;
    post<T = any>(url: string, options?: HttpRequestConfig, body?: any): Promise<HttpResponse<T>>;
    upload<T = any>(url: string, options?: HttpRequestConfig, file?: File): Promise<HttpResponse<T>>;
    put<T = any>(url: string, options?: HttpRequestConfig, body?: any): Promise<HttpResponse<T>>;
    delete<T = any>(url: string, options?: HttpRequestConfig): Promise<HttpResponse<T>>;
    performRequest<T = any>(method: string, url: string, options?: HttpRequestConfig, body?: any): Promise<HttpResponse<T>>;
};
export {};
