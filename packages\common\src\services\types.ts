// Common types for services

// HTTP Service types
export interface HttpRequestConfig {
  url?: string;
  method?: string;
  data?: any;
  params?: any;
  headers?: Record<string, string>;
  timeout?: number;
  withCredentials?: boolean;
  responseType?: string;
  [key: string]: any;
}

export interface HttpResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: HttpRequestConfig;
  request?: any;
}

export interface HttpError extends Error {
  config: HttpRequestConfig;
  code?: string;
  request?: any;
  response?: HttpResponse;
  isAxiosError?: boolean;
}

// Security Service types
export interface User {
  id?: string | number;
  username?: string;
  email?: string;
  roles?: string[];
  permissions?: string[];
  token?: string;
  [key: string]: any;
}

export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  token?: string;
  user?: User;
  expiresIn?: number;
  [key: string]: any;
}

// Log Service types
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  data?: any;
}

// Error Service types
export interface ServiceErrorInfo {
  message: string;
  code?: string | number;
  stack?: string;
  data?: any;
}

// Properties Service types
export interface Properties {
  [key: string]: string;
}

// SMS Service types
export interface SmsMessage {
  id?: string | number;
  to: string;
  from?: string;
  text: string;
  status?: string;
  createdAt?: Date;
  updatedAt?: Date;
  [key: string]: any;
}

// IMS Service types
export interface ImsMessage {
  id?: string | number;
  to: string;
  from?: string;
  content: string;
  type?: string;
  status?: string;
  createdAt?: Date;
  updatedAt?: Date;
  [key: string]: any;
}

// Language Service types
export interface Language {
  code: string;
  name: string;
  isRTL?: boolean;
  isDefault?: boolean;
}

// SMSC Service common types
export interface SmscEntity {
  id?: string | number;
  name?: string;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
  [key: string]: any;
}

// === Types moved from apps/smsws/src/types.ts ===

export enum StripAndReplaceType {
  A_NUMBER = 'A_NUMBER',
  B_NUMBER = 'B_NUMBER'
}

export enum TypeOfNumber {
  UNKNOWN = 'UNKNOWN',
  INTERNATIONAL = 'INTERNATIONAL',
  NATIONAL = 'NATIONAL',
  NETWORK_SPECIFIC = 'NETWORK_SPECIFIC',
  SUBSCRIBER_NUMBER = 'SUBSCRIBER_NUMBER',
  ALPHANUMERIC = 'ALPHANUMERIC',
  ABBREVIATED = 'ABBREVIATED'
}

export enum NumberPlanIndicator {
  UNKNOWN = 'UNKNOWN',
  ISDN = 'ISDN',
  DATA = 'DATA',
  TELEX = 'TELEX',
  LAND_MOBILE = 'LAND_MOBILE',
  NATIONAL = 'NATIONAL',
  PRIVATE = 'PRIVATE',
  ERMES = 'ERMES',
  INTERNET = 'INTERNET',
  WAP_CLIENT_ID = 'WAP_CLIENT_ID'
}

export interface StripAndReplaceEntry {
  id?: string | number;
  minDigits?: number;
  maxDigits?: number;
  stripTypeOfNumber?: TypeOfNumber;
  stripNumberPlanIndicator?: NumberPlanIndicator;
  stripNumber?: string;
  replaceTypeOfNumber?: TypeOfNumber;
  replaceNumberPlanIndicator?: NumberPlanIndicator;
  replaceNumber?: string;
  inEdit?: boolean;
  [key: string]: any;
}

export interface StripAndReplace {
  id?: string | number;
  moStripAndReplaceEnabled?: boolean;
  aoStripAndReplaceEnabled?: boolean;
  type?: StripAndReplaceType;
  stripAndReplaceEntries?: StripAndReplaceEntry[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

export interface EnumerationOption {
  value: string;
  displayText: string;
}

// ================================================
