import { ApplicationProfile } from "@pnmui/common/types/smscTypes";
export declare const SmscApplicationProfilesService: {
    getApplicationProfiles(): Promise<ApplicationProfile[]>;
    getSmppApplicationProfileById(id: string | number): Promise<ApplicationProfile>;
    saveSmppApplicationProfile(smppApplicationProfile: ApplicationProfile): Promise<ApplicationProfile | null>;
    saveSmppApplicationProfileWithName(smppApplicationProfile: {
        name: string;
    }): Promise<ApplicationProfile>;
    deleteSmppApplicationProfileById(id: string | number): Promise<boolean | null>;
};
