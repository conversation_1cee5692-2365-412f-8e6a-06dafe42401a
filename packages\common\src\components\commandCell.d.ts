import React from "react";
interface DataItem {
    id?: string | number;
    tempId?: string | number;
    name?: string;
    externalId?: string;
    inEdit?: boolean;
    [key: string]: any;
}
interface ParentItem {
    [key: string]: DataItem[];
}
interface CommandCellProps {
    dataItem: DataItem;
    item: ParentItem;
    gridProp: string;
    onChange: (data: DataItem[]) => void;
    onSave?: (dataItem: DataItem) => boolean;
    onCancel?: () => void;
    id?: string;
    colSpan?: number;
    ariaColumnIndex?: number;
    isSelected?: boolean;
    columnIndex?: number;
    dataIndex?: number;
}
declare const CommandCell: React.FC<CommandCellProps>;
export default CommandCell;
