import React, { MouseEvent, ReactElement, Ref } from "react";
interface TableColumn {
    field?: string;
    title?: string;
    render?: (rowData: any) => ReactElement | string | null;
    width?: number | string;
    [key: string]: any;
}
interface TableAction {
    icon?: React.ComponentType;
    tooltip?: string;
    onClick?: (event: MouseEvent, rowData: any) => void;
    render?: () => ReactElement | null;
    [key: string]: any;
}
interface TableOptions {
    pageSize?: number;
    hideCheckboxes?: boolean;
    [key: string]: any;
}
interface TableData {
    data: any[];
    totalCount: number;
}
interface MaterialTableProps {
    data?: (params: {
        page: number;
        pageSize: number;
    }) => Promise<TableData>;
    columns: TableColumn[];
    options: TableOptions;
    actions?: (TableAction | (() => TableAction))[];
    onRowClick?: (event: MouseEvent, rowData: any) => void;
    onSelectionChange?: (selectedRows: {
        id: string | number;
    }[]) => void;
    tableRef?: Ref<TableRefHandle>;
    enableDragAndDrop?: boolean;
    rows?: any[];
    onRowOrderChange?: (newRows: any[]) => void;
}
interface TableRefHandle {
    onQueryChange: (query: {
        page: number;
        pageSize: number;
    } | null) => void;
    refreshData: () => void;
}
declare const MaterialTable: React.ForwardRefExoticComponent<MaterialTableProps & React.RefAttributes<unknown>>;
export default MaterialTable;
