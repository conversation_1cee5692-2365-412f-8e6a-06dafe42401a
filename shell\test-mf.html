<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Micro Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background-color: #1976d2;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
        }
        .content {
            border: 1px solid #ddd;
            padding: 20px;
            min-height: 400px;
        }
        .loading {
            text-align: center;
            padding: 50px;
        }
        .error {
            color: red;
            padding: 20px;
            background-color: #ffebee;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Micro Frontend Test - Shell Application</h1>
            <p>Testing SMSWS micro frontend integration</p>
        </div>
        
        <div class="content">
            <div id="loading" class="loading">
                <p>Loading SMSWS micro frontend...</p>
                <p>Make sure SMSWS is running on http://localhost:3001</p>
            </div>
            
            <div id="error" class="error" style="display: none;">
                <h3>Error Loading Micro Frontend</h3>
                <p>Could not load the SMSWS micro frontend. Please check:</p>
                <ul>
                    <li>SMSWS micro frontend is running on http://localhost:3001</li>
                    <li>The remoteEntry.js file is accessible</li>
                    <li>CORS is properly configured</li>
                </ul>
            </div>
            
            <div id="app-container" style="display: none;">
                <!-- Micro frontend will be loaded here -->
            </div>
        </div>
    </div>

    <script type="module">
        async function loadMicroFrontend() {
            try {
                // Test if the remote entry is accessible
                const response = await fetch('http://localhost:3001/remoteEntry.js');
                if (!response.ok) {
                    throw new Error('Remote entry not accessible');
                }
                
                // Load the remote entry
                const script = document.createElement('script');
                script.src = 'http://localhost:3001/remoteEntry.js';
                script.onload = () => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('app-container').style.display = 'block';
                    document.getElementById('app-container').innerHTML = '<p>✅ Micro frontend loaded successfully!</p><p>Remote entry is accessible and can be integrated.</p>';
                };
                script.onerror = () => {
                    throw new Error('Failed to load remote entry script');
                };
                document.head.appendChild(script);
                
            } catch (error) {
                console.error('Error loading micro frontend:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
            }
        }

        // Start loading
        loadMicroFrontend();
    </script>
</body>
</html>
