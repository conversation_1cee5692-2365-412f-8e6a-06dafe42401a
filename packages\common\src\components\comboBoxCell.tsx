import React, { useState } from "react";
import { Autocomplete, TextField } from "@mui/material";

interface Option {
  name: string;
  inputValue?: string;
  [key: string]: any;
}

interface DataItem {
  [key: string]: any;
  inEdit?: boolean;
}

interface ChangeEvent {
  dataIndex: number;
  dataItem: DataItem;
  field: string;
  syntheticEvent: React.SyntheticEvent;
  value: any;
}

interface ComboBoxCellProps {
  dataItem: DataItem;
  field?: string;
  options: Option[];
  onChange?: (event: ChangeEvent) => void;
  onAddNew?: (value: string) => void;
  error?: string;
}

const ComboBoxCell: React.FC<ComboBoxCellProps> = (props) => {
  const [editItem, setEditItem] = useState<DataItem>(props.dataItem);
  const { dataItem, error } = props;
  const field = props.field || "";
  const dataValue =
    typeof dataItem[field] === "string"
      ? props.options.find((opt) => opt.name === dataItem[field])
      : dataItem[field];

  const handleItemChange = (e: React.SyntheticEvent, value: any): void => {
    if (props.field) {
      props.dataItem[props.field] = value;
      setEditItem(props.dataItem);
      if (props.onChange) {
        props.onChange({
          dataIndex: 0,
          dataItem: props.dataItem,
          field: props.field,
          syntheticEvent: e,
          value,
        });
      }
    }
  };

  return (
    <td>
      <div style={{ position: 'relative' }}>
        {dataItem.inEdit ? (
          <>
            <Autocomplete
              value={
                props.field && typeof editItem[props.field] === "string"
                  ? props.options.find((opt) => opt.name === editItem[props.field || ''])
                  : props.field ? editItem[props.field || ''] : null
              }
              onChange={(event: React.SyntheticEvent, newValue: string | Option | null) => {
                if (newValue && typeof newValue !== 'string' && 'inputValue' in newValue) {
                  handleItemChange(event, newValue.inputValue);
                  if (props.onAddNew && newValue.inputValue) {
                    props.onAddNew(newValue.inputValue);
                  }
                } else {
                  handleItemChange(
                    event,
                    typeof newValue === "string" ? newValue : newValue?.name || ""
                  );
                }
              }}
              filterOptions={(options: Option[], params: { inputValue: string }) => {
                const { inputValue } = params;
                const isExisting = options.some(
                  (option: Option) => inputValue === option.name
                );
                const filtered = [...options];
                if (inputValue !== "" && !isExisting) {
                  filtered.push({
                    inputValue,
                    name: `Add "${inputValue}"`,
                  });
                }
                return filtered;
              }}
              size="small"
              selectOnFocus
              clearOnBlur
              handleHomeEndKeys
              options={props.options}
              getOptionLabel={(option: string | Option) => {
                if (typeof option === "string") return option;
                if ('inputValue' in option && option.inputValue) return option.inputValue;
                return option.name;
              }}
              renderOption={(props: React.HTMLAttributes<HTMLLIElement>, option: Option) => {
                const optionProps = props;
                return (
                  <li {...optionProps}>
                    {option.name}
                  </li>
                );
              }}
              freeSolo
              renderInput={(params: any) => (
                <TextField
                  {...params}
                  error={!!error}
                  placeholder="Type for selecting or inserting new."
                />
              )}
            />
            {error && (
              <div className="error-message" style={{
                color: '#d32f2f',
                fontSize: '0.75rem',
                marginTop: '-1em',
                position: 'relative',
                left: '5%'
              }}>
                {error}
              </div>
            )}
          </>
        ) : (
          <span>{dataValue?.name || dataValue || ""}</span>
        )}
      </div>
    </td>
  );
};

export default ComboBoxCell;
