import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  MenuItem,
  Typography,
} from "@mui/material";
import React, { useEffect, useState, MouseEvent, ChangeEvent } from "react";
import { handleChange, handleSwitchChange } from "./common";
import { StorageFields } from "./StorageFields";
import { ApplicationProfile, ResourcePolicy } from "../types";

interface StorageSettings {
  enabled?: boolean;
  storagePolicy?: string;
  storageDuration?: number;
  storageDurationUnit?: string;
  resourcePolicy?: string | number | ResourcePolicy;
}

interface StorageAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  resourcePolicies: ResourcePolicy[];
  errors: Record<string, any>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

const StorageAccordion: React.FC<StorageAccordionProps> = ({
  applicationProfile,
  setApplicationProfile,
  resourcePolicies,
  errors,
  setErrors
}) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  useEffect(() => {
    setExpanded(applicationProfile.storage?.enabled || false);
  }, [applicationProfile.storage?.enabled]);

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.storage?.enabled;

    setApplicationProfile((prev) => ({
      ...prev,
      storage: {
        ...prev.storage,
        enabled: newEnabled,
      },
    }));

    setExpanded(newEnabled);

    // Clear errors when disabled
    if (!newEnabled && setErrors) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.storage;
        return newErrors;
      });
    }
  };

  const handleStorageChange = (e: ChangeEvent<HTMLInputElement>): void => {
    handleChange(e, setApplicationProfile);
  };

  const handleStorageSwitchChange = (e: ChangeEvent<HTMLInputElement>): void => {
    handleSwitchChange(e, setApplicationProfile);
  };

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{
              color: applicationProfile.storage?.enabled ? "#3f51b5" : "gray",
              fontSize: "3em",
            }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable Storage</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{ justifyContent: "start" }}
      >
        <StorageFields
          onChange={handleStorageChange}
          storage={applicationProfile.storage}
          propName="storage"
          map={resourcePolicies.map((option) => (
            <MenuItem key={option.id} value={option.id}>
              {option.name}
            </MenuItem>
          ))}
          onChangeSwitch={handleStorageSwitchChange}
          errors={errors?.storage}
          isEnabled={applicationProfile.storage?.enabled}
        />
      </AccordionDetails>
    </Accordion>
  );
};

export default StorageAccordion;
