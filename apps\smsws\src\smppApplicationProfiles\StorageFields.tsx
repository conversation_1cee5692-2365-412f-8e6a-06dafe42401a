import { FormControlLabel, MenuItem, Switch } from "@mui/material";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { ChangeEvent, useEffect, useState } from "react";
import { SelectValidator } from "react-material-ui-form-validator";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";

interface StorageFieldsProps {
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  storage: Record<string, any>;
  map?: Record<string, any>;
  onChangeSwitch: (e: ChangeEvent<HTMLInputElement>) => void;
  propName: string;
  errors?: Record<string, any>;
  isEnabled?: boolean;
}

export function StorageFields({
  onChange,
  storage,
  propName,
  map,
  onChangeSwitch,
  errors,
  isEnabled
}: StorageFieldsProps): JSX.Element {
  const [, setI18n] = useState<Record<string, string>>({});

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <>
      <FormControlLabel
        control={
          <Switch
            checked={storage?.enableMessageReassembly || false}
            onChange={onChangeSwitch}
            name={`${propName}.enableMessageReassembly`}
          />
        }
        label="Enable Message Reassembly"
      />
      <div className="flex-container" style={{ justifyContent: "start" }}>
        <SelectValidator
          label="Storage Resource Policy"
          onChange={onChange}
          name={`${propName}.storageResourcePolicy`}
          value={storage?.storageResourcePolicy || ""}
          validators={isEnabled ? ["required"] : []}
          errorMessages={isEnabled ? ["Storage resource policy is required"] : []}
          error={Boolean(errors?.storageResourcePolicy)}
          helperText={errors?.storageResourcePolicy}
          style={{ minWidth: 200 }}
        >
          <MenuItem value="">
            <em>None</em>
          </MenuItem>
          {map}
        </SelectValidator>
      </div>
      <div className="flex-container" style={{ justifyContent: "start" }}>
        <SelectValidator
          label="Consumer Resource Policy"
          onChange={onChange}
          name={`${propName}.consumerResourcePolicy`}
          value={storage?.consumerResourcePolicy || ""}
          validators={isEnabled ? ["required"] : []}
          errorMessages={isEnabled ? ["Consumer resource policy is required"] : []}
          error={Boolean(errors?.consumerResourcePolicy)}
          helperText={errors?.consumerResourcePolicy}
          style={{ minWidth: 200 }}
        >
          <MenuItem value="">
            <em>None</em>
          </MenuItem>
          {map}
        </SelectValidator>
      </div>
    </>
  );
}
