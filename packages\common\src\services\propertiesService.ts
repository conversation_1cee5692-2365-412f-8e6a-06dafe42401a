/* eslint no-underscore-dangle: 0 */
import { AxiosResponse } from "axios";
import { BehaviorSubject } from "rxjs";
import { HttpService } from "./httpService";
import { Properties } from "./types";

export const $properties = new BehaviorSubject<Properties>({});

export const $i18n = new BehaviorSubject<Record<string, string>>({});

const baseUrl = process.env.STANDALONE === 'false' ? window.location.href
    .substring(0, window.location.href.indexOf("react") - 1)
    .replace(window.location.origin, "")
  ? `${window.location.href
      .substring(0, window.location.href.indexOf("react") - 1)
      .replace(window.location.origin, "")}/properties`
  : "/pmi/properties" : `${process.env.PMI_URL ? process.env.PMI_URL.split('"').join('') : 'http://localhost:8080/pmi'}/properties`;

  console.log(  "PropertiesService process.env.PMI_URL:", process.env.PMI_URL);
  console.log(  "PropertiesService baseUrl:", baseUrl);

export const PropertiesService = {
  init(): Promise<void> {
    console.log("PropertiesService.init() called");
    console.log("baseUrl for properties:", baseUrl);
    console.log("process.env:", process.env);

    // Initialize HttpService with the properties object
    HttpService.init($properties);

    return new Promise<void>((resolve, reject) => {
      console.log("Starting to update config properties...");
      this.updateConfigProperties()
        .then(() => {
          console.log("Config properties updated successfully");
          console.log("Current properties:", $properties.value);
          console.log("Starting to update i18n properties...");
          this.updateI18nProperties()
            .then(() => {
              console.log("i18n properties updated successfully");
              console.log("Properties initialization complete");
              resolve();
            })
            .catch((error) => {
              console.error("Error updating i18n properties:", error);
              reject(error);
            });
        })
        .catch((error) => {
          console.error("Error updating config properties:", error);
          reject(error);
        });
    });
  },

  updateConfigProperties(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        console.log("Making HTTP request to get properties from:", baseUrl);
        HttpService.getWithoutRouting(baseUrl)
          .then((response: AxiosResponse<Properties>) => {
            console.log("Properties response status:", response.status);
            console.log("Properties response data:", response.data);

            // Check if client.smsws.base.url is in the response
            if (!response.data["client.smsws.base.url"]) {
              console.warn("WARNING: client.smsws.base.url is not set in the properties response!");
              // Don't set a default value, let the application handle the error properly
            }

            console.log("Updating properties with new data");
            $properties.next(response.data);
            console.log("Properties after update:", $properties.value);
            resolve();
          })
          .catch((err) => {
            console.error("HTTP error getting properties:", err);
            reject(err);
          });
      } catch (e) {
        console.error("Exception updating config properties:", e);
        reject(e);
      }
    });
  },

  updateI18nProperties(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        HttpService.getWithoutRouting(`${baseUrl}/i18n?lang=${this._getLang()}`)
          .then((response: AxiosResponse<Record<string, string>>) => {
            console.log(response);
            $i18n.next(response.data);
            resolve();
          })
          .catch((err) => {
            reject(err);
          });
      } catch (e) {
        console.error("Error updating i18n properties:", e);
        reject(e);
      }
    });
  },

  _getLang(): string {
    let lang = "en";
    const chosenLang = localStorage.getItem("pmi_lang");
    if (chosenLang) {
      lang = chosenLang;
    } else {
      try {
        if (navigator.languages && navigator.languages.length) {
          lang = navigator.languages[0].split("-")[0];
        } else if (navigator.language) lang = navigator.language.split("-")[0];
      } catch (e) {
        console.warn(
          "Could not obtain language from browser. Defaulting to en",
        );
      }
    }
    return lang;
  },
};
