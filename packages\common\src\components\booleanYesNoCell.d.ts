import React, { ReactElement } from 'react';
interface DataItem {
    [key: string]: any;
    inEdit?: boolean;
}
interface ChangeEvent {
    dataIndex: number;
    dataItem: DataItem;
    field: string;
    syntheticEvent: React.ChangeEvent<HTMLInputElement>;
    value: boolean;
}
interface BooleanYesNoCellProps {
    field?: string;
    dataItem: DataItem;
    id?: string;
    onChange?: (event: ChangeEvent) => void;
    colSpan?: number;
    ariaColumnIndex?: number;
    isSelected?: boolean;
    columnIndex?: number;
    render?: (content: ReactElement, props: BooleanYesNoCellProps) => ReactElement;
}
declare const BooleanYesNoCell: React.FC<BooleanYesNoCellProps>;
export default BooleanYesNoCell;
