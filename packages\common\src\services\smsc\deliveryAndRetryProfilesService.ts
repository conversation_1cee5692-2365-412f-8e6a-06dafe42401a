import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { DeliveryAndRetryProfile } from "@pnmui/common/types/smscTypes";

export const SmscDeliveryAndRetryProfilesService = {
  getDeliveryAndRetryProfiles(): Promise<DeliveryAndRetryProfile[]> {
    return new Promise((resolve, reject) => {
      HttpService.get<DeliveryAndRetryProfile[]>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetryProfiles`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  saveRetryProfileWithName(retryProfileName: string): Promise<DeliveryAndRetryProfile> {
    return new Promise((resolve, reject) => {
      const payload = { name: retryProfileName };
      HttpService.post<DeliveryAndRetryProfile>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetryProfiles/createWithName`,
        {},
        payload
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  getRetryProfileById(id: string | number): Promise<DeliveryAndRetryProfile> {
    return new Promise((resolve, reject) => {
      HttpService.get<DeliveryAndRetryProfile>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetryProfiles/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  saveRetryProfile(retryProfile: DeliveryAndRetryProfile): Promise<DeliveryAndRetryProfile> {
    return new Promise((resolve, reject) => {
      HttpService[retryProfile.id ? "put" : "post"]<DeliveryAndRetryProfile>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetryProfiles${
          retryProfile.id ? "/" + retryProfile.id : ""
        }`,
        {},
        retryProfile
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  deleteRetryProfileById(id: string | number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      HttpService.delete(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/deliveryAndRetryProfiles/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};


