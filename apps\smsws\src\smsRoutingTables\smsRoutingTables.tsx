import { <PERSON><PERSON>, <PERSON>, CardContent, <PERSON>Header, MenuItem } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscRoutingClassesService } from "@pnmui/common/services/smsc/routingClassesService";
import { SmscSmsRoutingTablesService } from "@pnmui/common/services/smsc/smsRoutingTablesService";
import { paginate } from "@pnmui/common/utils";
import React, { ChangeEvent, useEffect, useState } from "react";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import { RoutingClass, SmsRoutingTable } from "../types";

interface TableData {
  data: SmsRoutingTable[];
  page: number;
  totalCount: number;
}

function SmscSmsRoutingTables() {
  const [originalRoutingTables, setOriginalRoutingTables] = useState<SmsRoutingTable[]>([]);
  const [routingTables, setRoutingTables] = useState<SmsRoutingTable[]>([]);
  const [routingClasses, setRoutingClasses] = useState<RoutingClass[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const navigate = useNavigate();
  const tableRefRoutingTables = React.createRef<any>();

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });
    SmscRoutingClassesService.getRoutingClasses().then(setRoutingClasses);
    populateRoutingTables();

    return () => subscription.unsubscribe();
  }, []);

  async function populateRoutingTables(): Promise<void> {
    try {
      const routingTablesResponse =
        await SmscSmsRoutingTablesService.getSmsRoutingTables();
      const routingTablesList: SmsRoutingTable[] = Array.isArray(routingTablesResponse)
        ? routingTablesResponse
        : [];
      setRoutingTables(routingTablesList);
      setOriginalRoutingTables(routingTablesList);
    } catch (error) {
      console.error("Error fetching routingTables:", error);
    }
  }

  async function populateRoutingTablesData(query: { page: number; pageSize: number }): Promise<TableData> {
    const paginatedList = paginate(routingTables, query.page, query.pageSize);
    return {
      data: paginatedList,
      page: query.page,
      totalCount: routingTables.length,
    };
  }

  const handleRowSelection = (rows: SmsRoutingTable[]): void => {
    setSelectedRows(rows.map((row) => row.id || ''));
  };

  const handleDelete = async (): Promise<void> => {
    if (selectedRows.length === 0) {
      console.error("No rows selected for deletion.");
      return;
    }
    if (window.confirm("Are you sure you want to delete?")) {
      try {
        await Promise.all(
          selectedRows.map(async (id) => {
            return await SmscSmsRoutingTablesService.deleteSmsRoutingTableById(
              id
            );
          })
        );
        const updatedRoutingTables = routingTables.filter(
          (routingTable) => routingTable.id !== undefined && selectedRows.indexOf(routingTable.id) === -1
        );
        setRoutingTables([...updatedRoutingTables]);
        setSelectedRows([]);
        toast.success("Rows deleted successfully!");
      } catch (error: any) {
        console.error(error);
        toast.error(error.message);
      }
    }
  };

  useEffect(() => {
    tableRefRoutingTables.current &&
      tableRefRoutingTables.current.onQueryChange();
  }, [routingTables]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search(): void {
    const filteredRoutingTables = originalRoutingTables.filter((routingTable) =>
      routingTable.name?.toLowerCase().includes(selectedName.toLowerCase())
    );
    setRoutingTables(filteredRoutingTables);
  }

  return (
    <div className="wrapper">
      <p style={{
        padding: "1em 1.5em",
        color: "#666",
        lineHeight: "1.5",
        margin: "0 0 1em 0"
      }}>
        {i18n["smsc.smsRoutingTables.smsRoutingTableList.description"] || "A number of files point to the identifiers of Routing Tables e.g  GSM_P2P_mtForwarder.cfg, GSM_P2M_mtForwarder.cfg. Following deployment of the configuration, these files (GSM_P2P_mtForwarder.cfg, GSM_P2M_mtForwarder.cfg etc...) should be updated to the align with the newly generated ISMSC_moRoutingList.cfg  and ISMSC_moRoutingClass.cfg"}
      </p>
      <Card className="content-card">
        <div className="form-row">
          <div style={{ display: "flex", alignItems: "center" }}>
            <div>
              <ValidatorForm onSubmit={() => {}}>
                <SelectValidator
                  label={
                    i18n["smsc.smsRoutingTables.smsRoutingTableList.name"] ||
                    "Name"
                  }
                  margin="normal"
                  variant="outlined"
                  style={{
                    minWidth: "15em",
                    marginRight: "1rem",
                  }}
                  children={["Any", ...routingTables.map((c) => c.name)].map(
                    (name) => (
                      <MenuItem key={name} value={name === "Any" ? "" : name}>
                        {name}
                      </MenuItem>
                    )
                  )}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setSelectedName(e.target.value)}
                  value={selectedName}
                />
              </ValidatorForm>
            </div>
          </div>
        </div>

        <CardContent>
          <MaterialTable
            tableRef={tableRefRoutingTables}
            icons={tableIcons}
            data={populateRoutingTablesData}
            columns={[
              {
                field: "name",
                title:
                  i18n["smsc.routingTables.routingTableList.name"] || "Name",
              },
              {
                field: "p2aDefaultRoutingClass",
                title:
                  i18n["smsc.routingTables.p2aDefaultRoutingClass"] ||
                  "P2A Routing Class",
                render: (rowData) => {
                  return rowData.p2aDefaultRoutingClass
                    ? routingClasses.find(
                        (rc) => rc.id === rowData.p2aDefaultRoutingClass
                      ).name
                    : "";
                },
              },
              {
                field: "p2pDefaultRoutingClass",
                title:
                  i18n["smsc.routingTables.p2pDefaultRoutingClass"] ||
                  "P2P Routing Class",
                render: (rowData) => {
                  return rowData.p2pDefaultRoutingClass
                    ? routingClasses.find(
                        (rc) => rc.id === rowData.p2pDefaultRoutingClass
                      ).name
                    : "";
                },
              },
              {
                field: "bnumberPrefixOverrides",
                title:
                  i18n["smsc.routingTables.bnumberPrefixOverrides"] ||
                  "Prefix Overrides",
                render: (rowData) => {
                  return rowData.bnumberPrefixOverrides
                    ? Object.keys(rowData.bnumberPrefixOverrides)
                        .map((key) => {
                          return `${rowData.bnumberPrefixOverrides[key].prefix}`;
                        })
                        .join(", ")
                    : "";
                },
              },
            ]}
            options={{
              selection: true,
              actionsColumnIndex: -1,
              toolbar: false,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              emptyRowsWhenPaging: false,
              headerStyle: { fontWeight: "bold" },
            }}
            onRowClick={(event, rowData) => {
              navigate(`/smscSmsRoutingTableForm/${rowData.id}`);
            }}
            onSelectionChange={(rows) => handleRowSelection(rows)}
          />
        </CardContent>
        <Button
          variant="contained"
          color="primary"
          className="request-handler-add-button"
          aria-label="Add"
          onClick={() => navigate("/smscSmsRoutingTableForm/new")}
          style={{
            marginLeft: "15px",
            marginTop: "2rem",
            marginBottom: "1rem",
          }}
          disabled={
            !SecurityService.checkPermission(
              "SMSC_ROUTING_TABLES_CREATE_PERMISSION"
            )
          }
        >
          {i18n["button.add"] || "Add"}
        </Button>

        <span style={{ marginLeft: "10px" }}>
          <Button
            variant="contained"
            color="secondary"
            type="button"
            onClick={handleDelete}
            style={{ marginTop: "2rem", marginBottom: "1rem" }}
            className="request-handler-add-button"
            disabled={
              !SecurityService.checkPermission(
                "SMSC_ROUTING_TABLES_DELETE_PERMISSION"
              )
            }
          >
            {i18n["button.delete"] || "Delete"}
          </Button>
        </span>
      </Card>
    </div>
  );
}

export default SmscSmsRoutingTables;





