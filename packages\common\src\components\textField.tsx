import { TextField as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TextFieldProps as MuiTextFieldProps } from "@mui/material";
import React, { FC, ReactNode } from "react";

/**
 * Props for the TextField component
 */
interface TextFieldProps extends Omit<MuiTextFieldProps, 'select'> {
  select?: boolean;
  children?: ReactNode;
}

/**
 * A safer wrapper around Material-UI TextField that handles select property correctly
 * and prevents common errors with children and select properties
 */
const TextField: FC<TextFieldProps> = (props) => {
  // Extract select-related props to handle them separately
  const { select, children, ...otherProps } = props;
  
  // If this is a select field, ensure we have children
  if (select === true) {
    // Only render as select if we have children to avoid MUI errors
    if (React.Children.count(children) > 0) {
      return <MuiTextField select={true} {...otherProps}>{children}</MuiTextField>;
    } else {
      // Fallback to a regular text field if no children are provided
      console.warn("TextField with select=true was rendered without children. Falling back to regular text field.");
      return <MuiTextField {...otherProps} />;
    }
  }
  
  // For non-select fields, pass through all props except select
  return <MuiTextField {...otherProps}>{children}</MuiTextField>;
};

export default TextField;
