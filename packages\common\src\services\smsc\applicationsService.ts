import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { Application } from "@pnmui/common/types/smscTypes";

export const SmscApplicationsService = {
  getApplications(): Promise<Application[]> {
    return new Promise((resolve, reject) => {
      // Check if we have a valid base URL before making the request
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn(`Cannot make applications request - invalid base URL: ${baseUrl}`);
        reject(new Error('Missing or invalid base URL'));
        return;
      }

      HttpService.get<Application[]>(
        `${baseUrl}/applications`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error('Error fetching applications:', error);
          reject(error);
        });
    });
  },

  getApplicationById(id: string | number): Promise<Application> {
    return new Promise((resolve, reject) => {
      // Check if we have a valid base URL before making the request
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot make application by ID request - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      HttpService.get<Application>(
        `${baseUrl}/applications/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error(`Error fetching application with ID ${id}:`, error);
          reject(error);
        });
    });
  },

  saveApplication(application: Application): Promise<Application | null> {
    const permission = application.id
      ? "SMSC_APPLICATION_UPDATE_PERMISSION"
      : "SMSC_APPLICATION_CREATE_PERMISSION";

    return new Promise((resolve, reject) => {
      // Check if we have a valid base URL before making the request
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot save application - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      if (SecurityService.checkPermission(permission)) {
        HttpService[application.id ? "put" : "post"]<Application>(
          `${baseUrl}/applications${application.id ? "/" + application.id : ""}`,
          {},
          application
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(error => {
            console.error('Error saving application:', error);
            reject(error);
          });
      } else {
        console.error(`${permission} is required.`);
        resolve(null);
      }
    });
  },

  createApplicationWithName(application: { name: string }): Promise<Application> {
    return new Promise((resolve, reject) => {
      // Check if we have a valid base URL before making the request
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot create application with name - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      HttpService.post<Application>(
        `${baseUrl}/applications/createWithName`,
        {},
        application
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error('Error creating application with name:', error);
          reject(error);
        });
    });
  },

  deleteApplicationById(id: string | number): Promise<boolean | null> {
    return new Promise((resolve, reject) => {
      // Check if we have a valid base URL before making the request
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot delete application - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      if (
        SecurityService.checkPermission("SMSC_APPLICATION_DELETE_PERMISSION")
      ) {
        HttpService.delete(
          `${baseUrl}/applications/${id}`,
          {}
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(error => {
            console.error(`Error deleting application with ID ${id}:`, error);
            reject(error);
          });
      } else {
        console.error("SMSC_APPLICATION_DELETE_PERMISSION is required.");
        resolve(null);
      }
    });
  },


};


