import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
} from "@mui/material";
import { GridToolbar, GridItemChangeEvent } from "@progress/kendo-react-grid";
import { Grid, Column } from "../components/KendoGridWrapper";
import React, { useEffect, useState, MouseEvent } from "react";
import { toast } from "react-toastify";
import { CommandCell, DurationCell, InputCell } from "@pnmui/common";
import { validateBlackoutPeriod } from "./validation";
import { ApplicationProfile } from "../types";

interface BlackoutPeriod {
  id?: string | number;
  dayOfWeek?: string;
  startTime?: string;
  endTime?: string;
  inEdit?: boolean;
}

interface BlackoutSettings {
  enabled?: boolean;
  periods?: BlackoutPeriod[];
}

interface BlackoutsAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  errors: Record<string, any>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

const BlackoutsAccordion: React.FC<BlackoutsAccordionProps> = ({ applicationProfile, setApplicationProfile, errors, setErrors }) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  useEffect(() => {
    setExpanded(applicationProfile.blackouts?.enabled || false);
  }, [applicationProfile.blackouts?.enabled]);

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.blackouts?.enabled;
    setApplicationProfile((prev) => ({
      ...prev,
      blackouts: {
        ...prev.blackouts,
        enabled: newEnabled,
      },
    }));
    setExpanded(newEnabled);

    // Clear errors when disabled
    if (!newEnabled && setErrors) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.blackouts;
        return newErrors;
      });
    }
  };

  const getErrorMessage = (periodId: string | number | undefined, field: string): string | undefined => {
    if (!periodId) return undefined;
    return errors?.blackouts?.periods?.[periodId]?.[field];
  };

  const addNewPeriod = (): void => {
    const newPeriod: BlackoutPeriod = {
      id: `temp_${Date.now()}`,
      dayOfWeek: "",
      startTime: "",
      endTime: "",
      inEdit: true,
    };

    setApplicationProfile((prevProfile) => ({
      ...prevProfile,
      blackouts: {
        ...prevProfile.blackouts,
        periods: prevProfile.blackouts?.periods ? [newPeriod, ...prevProfile.blackouts?.periods] : [newPeriod],
      },
    }));
  };

  const onItemChange = (event: GridItemChangeEvent): void => {
    const { dataItem } = event;
    const field = event.field || "";
    const value = event.value;

    setApplicationProfile((prevProfile) => {
      const updatedPeriods = prevProfile.blackouts?.periods ? prevProfile.blackouts.periods.map((period) =>
        period.id === dataItem.id ? { ...period, [field]: value } : period
      ) : [];

      return {
        ...prevProfile,
        blackouts: {
          ...prevProfile.blackouts,
          periods: updatedPeriods,
        },
      };
    });
  };

  const handleSave = (dataItem: BlackoutPeriod): boolean => {
    const validationErrors = validateBlackoutPeriod(dataItem);
    if (Object.keys(validationErrors).length > 0) {
      if (setErrors) {
        setErrors((prev) => ({
          ...prev,
          blackouts: {
            ...prev?.blackouts,
            periods: {
              ...prev?.blackouts?.periods,
              [dataItem.id as string]: validationErrors
            }
          }
        }));
      }
      toast.error(Object.values(validationErrors)[0] as string);
      return false;
    }
    return true;
  };

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{
              color: applicationProfile.blackouts?.enabled ? "#3f51b5" : "gray",
              fontSize: "3em",
            }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable Blackouts</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{ justifyContent: "start", flexDirection: "column" }}
      >
        <Typography variant="body1">
          <span style={{ color: "red" }}>If</span> SMS are sent during the following periods
        </Typography>
        <Grid
          editField="inEdit"
          editable={true}
          onItemChange={onItemChange}
          style={{ width: "50em", marginTop: "1em" }}
          data={applicationProfile.blackouts?.periods}
        >
          <GridToolbar>
            <button
              type="button"
              className="k-primary k-button k-grid-edit-command"
              style={{ position: "absolute", right: "1em" }}
              onClick={addNewPeriod}
            >
              Add
            </button>
          </GridToolbar>
          <Column
            field="name"
            title="Name"
            editable={true}
            editor="text"
            cell={(props) => (
              <InputCell
                {...props}
                onChange={onItemChange}
                error={getErrorMessage(props.dataItem.id, 'name')}
              />
            )}
          />
          <Column
            field="duration"
            title="Duration"
            editable={true}
            cell={(props) => (
              <DurationCell
                {...props}
                error={getErrorMessage(props.dataItem.id, 'duration')}
                onUpdate={(dataItem, position) => {
                  const newPeriods = [...applicationProfile.blackouts?.periods];
                  newPeriods[position] = dataItem;
                  setApplicationProfile({
                    ...applicationProfile,
                    blackouts: {
                      ...applicationProfile.blackouts,
                      periods: newPeriods,
                    },
                  });
                }}
              />
            )}
          />
          <Column
            cell={(props) => (
              <CommandCell
                {...props}
                item={applicationProfile.blackouts}
                onChange={(data) => {
                  setApplicationProfile({
                    ...applicationProfile,
                    blackouts: {
                      ...applicationProfile.blackouts,
                      periods: data,
                    },
                  });
                }}
                onSave={handleSave}
                gridProp="periods"
              />
            )}
            filterable={false}
          />
        </Grid>
        <Typography variant="body1" style={{ marginTop: "1em" }}>
          <span style={{ color: "red" }}>Then</span> Block the SMS
        </Typography>
      </AccordionDetails>
    </Accordion>
  );
};

export default BlackoutsAccordion;
