/* eslint no-underscore-dangle: 0 */
import { BehaviorSubject } from "rxjs";
import { HttpService } from "./httpService";
import { $properties } from "./propertiesService";
import { $userPermissions, SecurityService } from "./securityService";
import { Language, Properties } from "./types";
import { HttpResponse } from "./types";

interface LanguageResponse {
  _embedded?: {
    languageList?: Language[];
  };
}

export const $languages = new BehaviorSubject<Language[]>([]);

export const LanguagesService = {
  spcmBaseUrl: "",

  updateLanguages(): Promise<void | Language[]> {
    return new Promise((resolve, reject) => {
      if (
        SecurityService.checkPermission(
          "SPCM_PLAN_NOTIFICATION_READ_PERMISSION",
        )
      ) {
        HttpService.get<LanguageResponse>(`${this.spcmBaseUrl}/pcc/spcm/languages`)
          .then((response: HttpResponse<LanguageResponse>) => {
            console.log("Languages", response);
            $languages.next(
              response.data &&
                response.data._embedded &&
                response.data._embedded.languageList || [],
            );
            resolve();
          })
          .catch((err) => {
            reject(err);
          });
      } else {
        console.error(
          "SPCM_PLAN_NOTIFICATION_READ_PERMISSION is required to retrieve Languages",
        );
        resolve([]);
      }
    });
  },
};

$properties.subscribe((props: Properties) => {
  if (props) {
    $userPermissions.subscribe((userPermissions: string[]) => {
      if (userPermissions && userPermissions.length) {
        LanguagesService.spcmBaseUrl = props["client.spcm.base.url"];
        LanguagesService.updateLanguages();
      }
    });
  }
});
