import { ReactNode } from "react";
import { QueryBuilderCommonProps } from "../../types";
interface GroupHeaderOptionProps extends QueryBuilderCommonProps {
    children?: ReactNode;
    isSelected?: boolean;
    onClick: (value: string) => void;
    value: string;
}
export declare function GroupHeaderOption({ children, isSelected, onClick, value }: GroupHeaderOptionProps): import("react/jsx-runtime").JSX.Element;
export {};
