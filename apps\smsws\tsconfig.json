{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "allowImportingTsExtensions": true, "noEmit": true, "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitAny": false, "skipLibCheck": true, "noImplicitThis": false, "noPropertyAccessFromIndexSignature": false, "exactOptionalPropertyTypes": false, "noFallthroughCasesInSwitch": false, "allowJs": true, "checkJs": false, "baseUrl": ".", "paths": {"@pnmui/common": ["../../packages/common/src"], "@pnmui/common/*": ["../../packages/common/src/*"]}}, "include": ["src", "../../packages/common/src/services/smsc/applicationsService.ts", "../../packages/common/src/services/smsc/connectionsService.ts", "../../packages/common/src/services/smsc/deliveryAndRetryProfilesService.ts", "../../packages/common/src/services/smsc/deliveryAndRetrySchedulesService.ts", "../../packages/common/src/services/smsc/enumerationsService.ts", "../../packages/common/src/services/smsc/ipListsService.ts", "../../packages/common/src/services/smsc/numberListsService.ts", "../../packages/common/src/services/smsc/resourcePoliciesService.ts", "../../packages/common/src/services/smsc/routingClassesService.ts", "../../packages/common/src/services/smsc/smppApplicationProfilesService.ts", "../../packages/common/src/services/smsc/smsRoutingTablesService.ts", "../../packages/common/src/services/smsc/spamWordsService.ts"], "references": [{"path": "../../packages/common"}]}