import { <PERSON><PERSON><PERSON><PERSON><PERSON>, GridItemChangeEvent } from "@progress/kendo-react-grid";
import { Grid, Column } from "../components/KendoGridWrapper";
import React from "react";
import { toast } from "react-toastify";
import { BooleanYes<PERSON>o<PERSON><PERSON>, <PERSON>mboBox<PERSON>ell, CommandCell, Input<PERSON>ell, SelectCell } from "@pnmui/common";
import { DeliveryAndRetrySchedule, EnumerationOption } from "../types";

interface ScheduleState {
  id?: string | number;
  name: string;
  sriRetryPriorityEnabled?: boolean;
  action?: string | EnumerationOption;
  delayAfterFailure?: number;
  nextState?: string | { id: string | number; name: string };
  inEdit?: boolean;
  stateExceptions?: any[];
}

interface Enumerations {
  deliveryAndRetryScheduleStateAction?: EnumerationOption[];
  [key: string]: EnumerationOption[] | undefined;
}

interface StateErrors {
  name?: string;
  action?: string;
  delayAfterFailure?: string;
  nextState?: string;
}

interface ScheduleStatesGridProps {
  retrySchedule: DeliveryAndRetrySchedule;
  enumerations: Enumerations;
  onStateChange: (event: GridItemChangeEvent) => void;
  onAddNew: () => void;
  setRetrySchedule: (schedule: DeliveryAndRetrySchedule) => void;
  validateState: (state: ScheduleState) => StateErrors;
  i18n: Record<string, string>;
  getErrorMessage: (stateId: string | number | undefined, field: string) => string | undefined;
}

export const ScheduleStatesGrid: React.FC<ScheduleStatesGridProps> = ({
  retrySchedule,
  enumerations,
  onStateChange,
  onAddNew,
  setRetrySchedule,
  validateState,
  i18n,
  getErrorMessage,
}) => {
  // Handle action change to clear errors when action is STOP
  const handleActionChange = (event: GridItemChangeEvent): void => {
    // Call the original change handler which will handle error clearing
    onStateChange(event);
  };

  // Custom display function to maintain consistent capitalization
  const displayActionText = (action: string | EnumerationOption | undefined): string => {
    if (!action) return "";
    const actionValue = typeof action === 'object' ? action.value : action;
    const enumAction = (enumerations.deliveryAndRetryScheduleStateAction || []).find(
      (a) => (a.value || a) === actionValue
    );
    return enumAction?.displayText || actionValue;
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "start",
      }}
    >
      <p>
        {i18n["smsc.retryScheduleForm.accordion.scheduleStates.configlabel"] ||
          "Configure a State Table to specify if/when retries are attempted"}
      </p>
      <p>
        {i18n["smsc.scheduleStatesGrid.deliveryAttempt"] ||
          "A delivery is attempted on entry to each State"}
      </p>
      <Grid
        editField="inEdit"
        editable={true}
        onItemChange={onStateChange}
        style={{ marginTop: "2em" }}
        data={
          retrySchedule.states && retrySchedule.states.length
            ? retrySchedule.states
            : []
        }
        total={retrySchedule.states?.length || 0}
      >
        <GridToolbar>
          <button
            type="button"
            className="k-primary k-button k-grid-edit-command"
            style={{ position: "absolute", right: "1em" }}
            onClick={onAddNew}
          >
            {i18n["button.add"] || "Add"}
          </button>
        </GridToolbar>
        <Column
          field="name"
          title={i18n["smsc.scheduleStatesGrid.column.name"] || "Name"}
          editable={true}
          cell={(props) => (
            <InputCell
              {...props}
              error={getErrorMessage(props.dataItem.id, 'name')}
            />
          )}
        />
        <Column
          field="sriRetryPriorityEnabled"
          title={
            i18n["smsc.scheduleStatesGrid.column.sriHighPriority"] ||
            "SRI High Priority"
          }
          editable={true}
          cell={(props) => (
            <BooleanYesNoCell
              {...props}
              onChange={(e: { dataItem: any; value: boolean; syntheticEvent: React.SyntheticEvent }) =>
                onStateChange({
                  dataItem: e.dataItem,
                  field: "sriRetryPriorityEnabled",
                  value: e.value,
                  syntheticEvent: e.syntheticEvent,
                })
              }
            />
          )}
        />
        <Column
          field="action"
          title={i18n["smsc.scheduleExceptionsGrid.column.action"] || "Action"}
          editable={true}
          filterable={false}
          cell={(props) => (
            <SelectCell
              {...props}
              options={enumerations.deliveryAndRetryScheduleStateAction || []}
              value={props.dataItem}
              displayFunc={displayActionText}
              error={getErrorMessage(props.dataItem.id, 'action')}
              onChange={handleActionChange}
            />
          )}
        />
        <Column
          field="delayAfterFailure"
          mandatory={true}
          required={true}
          title={
            i18n["smsc.scheduleStatesGrid.column.delayBeforeNextState"] ||
            "Delay before calling next state(seconds)"
          }
          editable={true}
          cell={(props) => (
            <InputCell
              {...props}
              type="number"
              error={getErrorMessage(props.dataItem.id, 'delayAfterFailure')}
            />
          )}
        />
        <Column
          field="nextState"
          title={
            i18n["smsc.scheduleStatesGrid.column.nextState"] || "Next State"
          }
          editable={true}
          mandatory={true}
          required={true}
          cell={(props) => (
            <ComboBoxCell
              {...props}
              onAddNew={(newValue) =>
                setRetrySchedule({
                  ...retrySchedule,
                  states: [...retrySchedule.states, { name: newValue }],
                })
              }
              options={retrySchedule.states}
              value={props.dataItem}
              error={getErrorMessage(props.dataItem.id, 'nextState')}
            />
          )}
        />
        <Column
          cell={(props) => (
            <CommandCell
              {...props}
              item={{ states: retrySchedule.states }}
              onChange={(data: ScheduleState[]) => {
                setRetrySchedule({
                  ...retrySchedule,
                  states: data,
                });
              }}
              onSave={(dataItem: ScheduleState) => {
                const errors = validateState(dataItem);

                // Don't show nextState and delayAfterFailure errors if action is STOP
                if (dataItem.action === "STOP" ||
                    (typeof dataItem.action === 'object' && dataItem.action?.value === "STOP")) {
                  delete errors.nextState;
                  delete errors.delayAfterFailure;
                }

                if (Object.keys(errors).length > 0) {
                  const firstError = Object.values(errors)[0];
                  if (firstError) toast.error(firstError);
                  return false;
                }
                return true;
              }}
              gridProp="states"
            />
          )}
          filterable={false}
        />
      </Grid>
    </div>
  );
};
